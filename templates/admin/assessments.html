<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <title>心理测评管理 - 心理辅导护航平台</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- 引入字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- 引入图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #4e89ae;
            --secondary-color: #43658b;
            --light-color: #f5f5f5;
            --dark-color: #333;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --border-radius: 8px;
            --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            background-color: #f9f9f9;
            padding: 0;
            margin: 0;
        }
        
        .container {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background-color: var(--dark-color);
            color: white;
            padding: 20px 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }
        
        .sidebar-title {
            font-size: 20px;
            font-weight: 700;
            color: white;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .sidebar-title i {
            font-size: 24px;
            color: var(--primary-color);
        }
        
        .sidebar-menu {
            list-style: none;
        }
        
        .sidebar-menu-item {
            margin-bottom: 5px;
        }
        
        .sidebar-menu-link {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 20px;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: var(--transition);
        }
        
        .sidebar-menu-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .sidebar-menu-link.active {
            background-color: var(--primary-color);
            color: white;
            font-weight: 500;
        }
        
        .sidebar-menu-link i {
            width: 20px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 20px;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 700;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .user-name {
            font-weight: 500;
        }
        
        .card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .card-header {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-title {
            font-size: 18px;
            font-weight: 600;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .assessment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }
        
        .assessment-card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow: hidden;
        }
        
        .assessment-header {
            padding: 15px 20px;
            background-color: var(--primary-color);
            color: white;
        }
        
        .assessment-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .assessment-id {
            font-size: 14px;
            opacity: 0.8;
        }
        
        .assessment-body {
            padding: 20px;
        }
        
        .assessment-stats {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .stat {
            flex: 1;
            background-color: var(--light-color);
            padding: 10px;
            border-radius: var(--border-radius);
            text-align: center;
        }
        
        .stat-number {
            font-size: 20px;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        
        .assessment-results {
            margin-top: 20px;
        }
        
        .results-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
        }
        
        .results-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f5f5f5;
        }
        
        .result-name {
            font-weight: 500;
        }
        
        .result-count {
            background-color: var(--light-color);
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .assessment-actions {
            margin-top: 20px;
            display: flex;
            gap: 10px;
        }
        
        .btn {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            padding: 8px 15px;
            border-radius: var(--border-radius);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background-color: var(--secondary-color);
        }
        
        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
        }
        
        .btn-secondary {
            background-color: #f5f5f5;
            color: var(--dark-color);
            border: 1px solid #ddd;
        }
        
        .btn-secondary:hover {
            background-color: #e5e5e5;
        }
        
        .btn-danger {
            background-color: var(--danger-color);
        }
        
        .btn-danger:hover {
            background-color: #bd2130;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
                position: static;
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .assessment-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-title">
                    <i class="fas fa-brain"></i>
                    <span>管理后台</span>
                </div>
            </div>
            
            <ul class="sidebar-menu">
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('admin_dashboard') }}" class="sidebar-menu-link">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>仪表盘</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('admin_questions') }}" class="sidebar-menu-link">
                        <i class="fas fa-question-circle"></i>
                        <span>问题管理</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('admin_users') }}" class="sidebar-menu-link">
                        <i class="fas fa-users"></i>
                        <span>用户管理</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('admin_analytics') }}" class="sidebar-menu-link">
                        <i class="fas fa-chart-bar"></i>
                        <span>数据分析</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('admin_assessments') }}" class="sidebar-menu-link active">
                        <i class="fas fa-clipboard-check"></i>
                        <span>心理测评</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('admin_assessment_results') }}" class="sidebar-menu-link">
                        <i class="fas fa-poll"></i>
                        <span>测评结果</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('index') }}" class="sidebar-menu-link">
                        <i class="fas fa-home"></i>
                        <span>返回前台</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('logout') }}" class="sidebar-menu-link">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>退出登录</span>
                    </a>
                </li>
            </ul>
        </div>
        
        <div class="main-content">
            <div class="page-header">
                <h1 class="page-title">心理测评管理</h1>
                <div class="user-info">
                    <i class="fas fa-user-circle"></i>
                    <span class="user-name">{{ current_user.username }}</span>
                </div>
            </div>
            
            <div class="assessment-grid">
                {% for assessment in assessments %}
                    <div class="assessment-card">
                        <div class="assessment-header">
                            <h2 class="assessment-title">{{ assessment.title }}</h2>
                            <div class="assessment-id">ID: {{ assessment.id }}</div>
                        </div>
                        <div class="assessment-body">
                            <div class="assessment-stats">
                                <div class="stat">
                                    <div class="stat-number">{{ assessment.questions|length }}</div>
                                    <div class="stat-label">问题数</div>
                                </div>
                                <div class="stat">
                                    <div class="stat-number">{{ assessment.total_results }}</div>
                                    <div class="stat-label">测评次数</div>
                                </div>
                            </div>
                            
                            <div class="assessment-results">
                                <h3 class="results-title">结果分布</h3>
                                <div class="results-list">
                                    {% for result_type, count in assessment.result_counts.items() %}
                                        <div class="result-item">
                                            <div class="result-name">{{ result_type }}</div>
                                            <div class="result-count">{{ count }} 人</div>
                                        </div>
                                    {% else %}
                                        <div class="result-item">
                                            <div class="result-name">暂无数据</div>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                            
                            <div class="assessment-actions">
                                <a href="{{ url_for('admin_assessment_results') }}" class="btn">
                                    <i class="fas fa-poll"></i> 查看结果
                                </a>
                                <a href="{{ url_for('assessment_take', assessment_id=assessment.id) }}" class="btn btn-secondary" target="_blank">
                                    <i class="fas fa-external-link-alt"></i> 测试
                                </a>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>
</body>
</html>

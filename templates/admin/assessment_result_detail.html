<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <title>测评结果详情 - 心理辅导护航平台</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- 引入字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- 引入图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #4e89ae;
            --secondary-color: #43658b;
            --light-color: #f5f5f5;
            --dark-color: #333;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --border-radius: 8px;
            --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            background-color: #f9f9f9;
            padding: 0;
            margin: 0;
        }
        
        .container {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background-color: var(--dark-color);
            color: white;
            padding: 20px 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }
        
        .sidebar-title {
            font-size: 20px;
            font-weight: 700;
            color: white;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .sidebar-title i {
            font-size: 24px;
            color: var(--primary-color);
        }
        
        .sidebar-menu {
            list-style: none;
        }
        
        .sidebar-menu-item {
            margin-bottom: 5px;
        }
        
        .sidebar-menu-link {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 20px;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: var(--transition);
        }
        
        .sidebar-menu-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .sidebar-menu-link.active {
            background-color: var(--primary-color);
            color: white;
            font-weight: 500;
        }
        
        .sidebar-menu-link i {
            width: 20px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 20px;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 700;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .user-name {
            font-weight: 500;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .breadcrumb a {
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .breadcrumb i {
            font-size: 12px;
            color: #888;
        }
        
        .card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .card-header {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: var(--primary-color);
            color: white;
        }
        
        .card-title {
            font-size: 18px;
            font-weight: 600;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .info-section {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .info-item {
            background-color: var(--light-color);
            padding: 15px;
            border-radius: var(--border-radius);
        }
        
        .info-label {
            font-weight: 500;
            margin-bottom: 5px;
            color: #666;
        }
        
        .info-value {
            font-size: 16px;
            font-weight: 600;
        }
        
        .result-summary {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .result-score {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-weight: 700;
            color: white;
        }
        
        .score-normal {
            background-color: var(--success-color);
        }
        
        .score-mild {
            background-color: var(--warning-color);
        }
        
        .score-moderate {
            background-color: #d39e00;
        }
        
        .score-severe {
            background-color: var(--danger-color);
        }
        
        .score-number {
            font-size: 36px;
            line-height: 1;
        }
        
        .score-text {
            font-size: 16px;
            margin-top: 5px;
        }
        
        .result-info {
            flex: 1;
        }
        
        .result-label {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .result-description {
            color: #666;
            font-size: 16px;
            line-height: 1.6;
        }
        
        .answers-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .answer-item {
            display: flex;
            gap: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f5f5f5;
        }
        
        .answer-question {
            flex: 1;
            font-weight: 500;
        }
        
        .answer-value {
            width: 80px;
            text-align: center;
            padding: 3px 8px;
            border-radius: 4px;
            background-color: var(--light-color);
        }
        
        .btn {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            padding: 10px 20px;
            border-radius: var(--border-radius);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            font-size: 16px;
        }
        
        .btn:hover {
            background-color: var(--secondary-color);
        }
        
        .btn-secondary {
            background-color: #f5f5f5;
            color: var(--dark-color);
            border: 1px solid #ddd;
        }
        
        .btn-secondary:hover {
            background-color: #e5e5e5;
        }
        
        .btn-danger {
            background-color: var(--danger-color);
        }
        
        .btn-danger:hover {
            background-color: #bd2130;
        }
        
        .actions {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
                position: static;
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .result-summary {
                flex-direction: column;
                align-items: center;
                text-align: center;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-title">
                    <i class="fas fa-brain"></i>
                    <span>管理后台</span>
                </div>
            </div>
            
            <ul class="sidebar-menu">
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('admin_dashboard') }}" class="sidebar-menu-link">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>仪表盘</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('admin_questions') }}" class="sidebar-menu-link">
                        <i class="fas fa-question-circle"></i>
                        <span>问题管理</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('admin_users') }}" class="sidebar-menu-link">
                        <i class="fas fa-users"></i>
                        <span>用户管理</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('admin_analytics') }}" class="sidebar-menu-link">
                        <i class="fas fa-chart-bar"></i>
                        <span>数据分析</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('admin_assessments') }}" class="sidebar-menu-link">
                        <i class="fas fa-clipboard-check"></i>
                        <span>心理测评</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('admin_assessment_results') }}" class="sidebar-menu-link active">
                        <i class="fas fa-poll"></i>
                        <span>测评结果</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('index') }}" class="sidebar-menu-link">
                        <i class="fas fa-home"></i>
                        <span>返回前台</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('logout') }}" class="sidebar-menu-link">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>退出登录</span>
                    </a>
                </li>
            </ul>
        </div>
        
        <div class="main-content">
            <div class="page-header">
                <h1 class="page-title">测评结果详情</h1>
                <div class="user-info">
                    <i class="fas fa-user-circle"></i>
                    <span class="user-name">{{ current_user.username }}</span>
                </div>
            </div>
            
            <div class="breadcrumb">
                <a href="{{ url_for('admin_dashboard') }}">仪表盘</a>
                <i class="fas fa-chevron-right"></i>
                <a href="{{ url_for('admin_assessment_results') }}">测评结果</a>
                <i class="fas fa-chevron-right"></i>
                <span>结果详情</span>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">{{ assessment.title }} - 测评结果</h2>
                </div>
                <div class="card-body">
                    <div class="info-section">
                        <h3 class="section-title">基本信息</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">用户名</div>
                                <div class="info-value">{{ user.username }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">邮箱</div>
                                <div class="info-value">{{ user.email }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">测评时间</div>
                                <div class="info-value">{{ result.timestamp.split('T')[0] }} {{ result.timestamp.split('T')[1].split('.')[0] }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">测评ID</div>
                                <div class="info-value">{{ result.id }}</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="result-summary">
                        <div class="result-score 
                            {% if result.result == '正常' %}score-normal
                            {% elif result.result == '轻度抑郁' or result.result == '轻度焦虑' %}score-mild
                            {% elif result.result == '中度抑郁' or result.result == '中度焦虑' %}score-moderate
                            {% elif result.result == '重度抑郁' or result.result == '重度焦虑' %}score-severe
                            {% endif %}">
                            <div class="score-number">{{ result.score }}</div>
                            <div class="score-text">{{ result.result }}</div>
                        </div>
                        
                        <div class="result-info">
                            <div class="result-label">测评结果解读</div>
                            <div class="result-description">
                                {{ result.description }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="info-section">
                        <h3 class="section-title">回答详情</h3>
                        <div class="answers-list">
                            {% for question in assessment.questions %}
                                <div class="answer-item">
                                    <div class="answer-question">
                                        {{ question.id }}. {{ question.text }}
                                    </div>
                                    <div class="answer-value">
                                        {% set answer_value = result.answers[question.id|string]|int %}
                                        {% for option in assessment.options %}
                                            {% if option.value == answer_value %}
                                                {{ option.text }}
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <div class="actions">
                        <a href="{{ url_for('admin_assessment_results') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                        <button class="btn btn-danger" onclick="deleteResult('{{ result.id }}')">
                            <i class="fas fa-trash"></i> 删除结果
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 删除结果
        function deleteResult(resultId) {
            if (confirm('确定要删除这个测评结果吗？此操作不可撤销。')) {
                fetch(`/api/admin/assessment_results/${resultId}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('测评结果已成功删除');
                        window.location.href = "{{ url_for('admin_assessment_results') }}";
                    } else {
                        alert('删除失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除时发生错误，请稍后再试');
                });
            }
        }
    </script>
</body>
</html>

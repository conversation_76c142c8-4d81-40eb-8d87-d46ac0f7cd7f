<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <title>用户管理 - 心理辅导护航平台</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- 引入字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- 引入图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #4e89ae;
            --secondary-color: #43658b;
            --light-color: #f5f5f5;
            --dark-color: #333;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --border-radius: 8px;
            --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            background-color: #f9f9f9;
            padding: 0;
            margin: 0;
        }
        
        .container {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background-color: var(--dark-color);
            color: white;
            padding: 20px 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }
        
        .sidebar-title {
            font-size: 20px;
            font-weight: 700;
            color: white;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .sidebar-title i {
            font-size: 24px;
            color: var(--primary-color);
        }
        
        .sidebar-menu {
            list-style: none;
        }
        
        .sidebar-menu-item {
            margin-bottom: 5px;
        }
        
        .sidebar-menu-link {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 20px;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: var(--transition);
        }
        
        .sidebar-menu-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .sidebar-menu-link.active {
            background-color: var(--primary-color);
            color: white;
            font-weight: 500;
        }
        
        .sidebar-menu-link i {
            width: 20px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 20px;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 700;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .user-name {
            font-weight: 500;
        }
        
        .card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 20px;
        }
        
        .card-header {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-title {
            font-size: 18px;
            font-weight: 600;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th, .table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .table th {
            font-weight: 600;
            color: #666;
        }
        
        .table tr:last-child td {
            border-bottom: none;
        }
        
        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .badge-primary {
            background-color: rgba(78, 137, 174, 0.1);
            color: var(--primary-color);
        }
        
        .badge-success {
            background-color: rgba(40, 167, 69, 0.1);
            color: var(--success-color);
        }
        
        .badge-warning {
            background-color: rgba(255, 193, 7, 0.1);
            color: var(--warning-color);
        }
        
        .badge-danger {
            background-color: rgba(220, 53, 69, 0.1);
            color: var(--danger-color);
        }
        
        .btn {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            padding: 8px 15px;
            border-radius: var(--border-radius);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background-color: var(--secondary-color);
        }
        
        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
        }
        
        .btn-secondary {
            background-color: #f5f5f5;
            color: var(--dark-color);
            border: 1px solid #ddd;
        }
        
        .btn-secondary:hover {
            background-color: #e5e5e5;
        }
        
        .btn-danger {
            background-color: var(--danger-color);
        }
        
        .btn-danger:hover {
            background-color: #bd2130;
        }
        
        .btn-success {
            background-color: var(--success-color);
        }
        
        .btn-success:hover {
            background-color: #218838;
        }
        
        .search-box {
            margin-bottom: 20px;
        }
        
        .search-input {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-size: 16px;
            font-family: inherit;
            transition: var(--transition);
        }
        
        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(78, 137, 174, 0.2);
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
                position: static;
            }
            
            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-title">
                    <i class="fas fa-brain"></i>
                    <span>管理后台</span>
                </div>
            </div>
            
            <ul class="sidebar-menu">
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('admin_dashboard') }}" class="sidebar-menu-link">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>仪表盘</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('admin_questions') }}" class="sidebar-menu-link">
                        <i class="fas fa-question-circle"></i>
                        <span>问题管理</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('admin_users') }}" class="sidebar-menu-link active">
                        <i class="fas fa-users"></i>
                        <span>用户管理</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('admin_analytics') }}" class="sidebar-menu-link">
                        <i class="fas fa-chart-bar"></i>
                        <span>数据分析</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('index') }}" class="sidebar-menu-link">
                        <i class="fas fa-home"></i>
                        <span>返回前台</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('logout') }}" class="sidebar-menu-link">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>退出登录</span>
                    </a>
                </li>
            </ul>
        </div>
        
        <div class="main-content">
            <div class="page-header">
                <h1 class="page-title">用户管理</h1>
                <div class="user-info">
                    <i class="fas fa-user-circle"></i>
                    <span class="user-name">{{ current_user.username }}</span>
                </div>
            </div>
            
            <div class="search-box">
                <input type="text" id="search-input" class="search-input" placeholder="搜索用户..." onkeyup="searchUsers()">
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">所有用户</h2>
                </div>
                <div class="card-body">
                    <table class="table" id="users-table">
                        <thead>
                            <tr>
                                <th>用户名</th>
                                <th>邮箱</th>
                                <th>注册时间</th>
                                <th>角色</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users %}
                            <tr>
                                <td>{{ user.username }}</td>
                                <td>{{ user.email }}</td>
                                <td>{{ user.created_at.split('T')[0] }}</td>
                                <td>
                                    {% if user.is_admin %}
                                    <span class="badge badge-primary">管理员</span>
                                    {% else %}
                                    <span class="badge badge-secondary">普通用户</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.id != current_user.id %}
                                        <button class="btn btn-sm {% if user.is_admin %}btn-secondary{% else %}btn-success{% endif %}" 
                                                onclick="toggleAdmin('{{ user.id }}', {% if user.is_admin %}false{% else %}true{% endif %})">
                                            {% if user.is_admin %}
                                            <i class="fas fa-user"></i> 取消管理员
                                            {% else %}
                                            <i class="fas fa-user-shield"></i> 设为管理员
                                            {% endif %}
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="deleteUser('{{ user.id }}')">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>
                                    {% else %}
                                        <span class="badge badge-warning">当前用户</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 切换管理员权限
        function toggleAdmin(userId, makeAdmin) {
            const action = makeAdmin ? '设为管理员' : '取消管理员权限';
            if (confirm(`确定要将此用户${action}吗？`)) {
                fetch(`/api/admin/users/${userId}/admin`, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`用户已成功${action}`);
                        location.reload();
                    } else {
                        alert('操作失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('操作时发生错误，请稍后再试');
                });
            }
        }
        
        // 删除用户
        function deleteUser(userId) {
            if (confirm('确定要删除这个用户吗？此操作不可撤销。')) {
                fetch(`/api/admin/users/${userId}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('用户已成功删除');
                        location.reload();
                    } else {
                        alert('删除失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除时发生错误，请稍后再试');
                });
            }
        }
        
        // 搜索用户
        function searchUsers() {
            const input = document.getElementById('search-input');
            const filter = input.value.toUpperCase();
            const table = document.getElementById('users-table');
            const tr = table.getElementsByTagName('tr');
            
            for (let i = 0; i < tr.length; i++) {
                const usernameColumn = tr[i].getElementsByTagName('td')[0];
                const emailColumn = tr[i].getElementsByTagName('td')[1];
                
                if (usernameColumn && emailColumn) {
                    const usernameText = usernameColumn.textContent || usernameColumn.innerText;
                    const emailText = emailColumn.textContent || emailColumn.innerText;
                    
                    if (usernameText.toUpperCase().indexOf(filter) > -1 || emailText.toUpperCase().indexOf(filter) > -1) {
                        tr[i].style.display = '';
                    } else {
                        tr[i].style.display = 'none';
                    }
                }
            }
        }
    </script>
</body>
</html>

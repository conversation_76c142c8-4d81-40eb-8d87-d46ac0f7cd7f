<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <title>数据分析 - 心理辅导护航平台</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- 引入字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- 引入图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #4e89ae;
            --secondary-color: #43658b;
            --light-color: #f5f5f5;
            --dark-color: #333;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --border-radius: 8px;
            --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            background-color: #f9f9f9;
            padding: 0;
            margin: 0;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 250px;
            background-color: var(--dark-color);
            color: white;
            padding: 20px 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .sidebar-title {
            font-size: 20px;
            font-weight: 700;
            color: white;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .sidebar-title i {
            font-size: 24px;
            color: var(--primary-color);
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu-item {
            margin-bottom: 5px;
        }

        .sidebar-menu-link {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 20px;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: var(--transition);
        }

        .sidebar-menu-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .sidebar-menu-link.active {
            background-color: var(--primary-color);
            color: white;
            font-weight: 500;
        }

        .sidebar-menu-link i {
            width: 20px;
            text-align: center;
        }

        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 20px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        .page-title {
            font-size: 24px;
            font-weight: 700;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .user-name {
            font-weight: 500;
        }

        .card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 20px;
        }

        .card-header {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
        }

        .card-body {
            padding: 20px;
        }

        .chart-container {
            width: 100%;
            margin-bottom: 30px;
            text-align: center;
        }

        .chart-image {
            max-width: 100%;
            height: auto;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }

        .chart-description {
            margin-top: 15px;
            color: #666;
            font-size: 14px;
            text-align: center;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
                position: static;
            }

            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-title">
                    <i class="fas fa-brain"></i>
                    <span>管理后台</span>
                </div>
            </div>

            <ul class="sidebar-menu">
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('admin_dashboard') }}" class="sidebar-menu-link">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>仪表盘</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('admin_questions') }}" class="sidebar-menu-link">
                        <i class="fas fa-question-circle"></i>
                        <span>问题管理</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('admin_users') }}" class="sidebar-menu-link">
                        <i class="fas fa-users"></i>
                        <span>用户管理</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('admin_analytics') }}" class="sidebar-menu-link active">
                        <i class="fas fa-chart-bar"></i>
                        <span>数据分析</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('index') }}" class="sidebar-menu-link">
                        <i class="fas fa-home"></i>
                        <span>返回前台</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{{ url_for('logout') }}" class="sidebar-menu-link">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>退出登录</span>
                    </a>
                </li>
            </ul>
        </div>

        <div class="main-content">
            <div class="page-header">
                <h1 class="page-title">数据分析</h1>
                <div class="user-info">
                    <i class="fas fa-user-circle"></i>
                    <span class="user-name">{{ current_user.username }}</span>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">平台统计数据</h2>
                </div>
                <div class="card-body">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-question-circle"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number">{{ total_questions }}</div>
                                <div class="stat-label">问题总数</div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-comment"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number">{{ total_answers }}</div>
                                <div class="stat-label">回答总数</div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-eye"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number">{{ total_views }}</div>
                                <div class="stat-label">浏览总数</div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-thumbs-up"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number">{{ total_votes }}</div>
                                <div class="stat-label">投票总数</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">问题和回答数量随时间变化</h2>
                </div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>日期</th>
                                <th>问题数量</th>
                                <th>回答数量</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in date_data %}
                            <tr>
                                <td>{{ item.date }}</td>
                                <td>{{ item.questions }}</td>
                                <td>{{ item.answers }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">浏览量分布</h2>
                </div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>浏览量范围</th>
                                <th>问题数量</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for range, count in view_stats.items() %}
                            <tr>
                                <td>{{ range }}</td>
                                <td>{{ count }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">投票数分布</h2>
                </div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>投票数范围</th>
                                <th>问题数量</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for range, count in vote_stats.items() %}
                            <tr>
                                <td>{{ range }}</td>
                                <td>{{ count }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

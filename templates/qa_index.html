<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <title>心理辅导护航平台 - 问答社区</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- 引入字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- 引入图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 引入Markdown渲染库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <!-- 引入代码高亮 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/github.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <style>
        :root {
            --primary-color: #4e89ae;
            --secondary-color: #43658b;
            --light-color: #f5f5f5;
            --dark-color: #333;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --border-radius: 8px;
            --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            background-color: #f9f9f9;
            padding: 0;
            margin: 0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            margin-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo i {
            font-size: 24px;
            color: var(--primary-color);
        }

        h1 {
            font-size: 24px;
            font-weight: 700;
            color: var(--primary-color);
            margin: 0;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: var(--dark-color);
            text-decoration: none;
            padding: 5px 10px;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .nav-links a:hover {
            background-color: var(--light-color);
        }

        .nav-links a.active {
            color: var(--primary-color);
            font-weight: 500;
        }

        .user-menu {
            position: relative;
        }

        .user-menu-button {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 5px 10px;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
        }

        .user-menu-button:hover {
            background-color: var(--light-color);
        }

        .user-menu-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            min-width: 180px;
            z-index: 100;
            display: none;
        }

        .user-menu-dropdown.show {
            display: block;
        }

        .user-menu-item {
            display: block;
            padding: 10px 15px;
            text-decoration: none;
            color: var(--dark-color);
            transition: var(--transition);
        }

        .user-menu-item:hover {
            background-color: var(--light-color);
        }

        .user-menu-item.danger {
            color: var(--danger-color);
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--dark-color);
        }

        .btn {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            padding: 10px 20px;
            border-radius: var(--border-radius);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            font-size: 16px;
        }

        .btn:hover {
            background-color: var(--secondary-color);
        }

        .btn-secondary {
            background-color: #f5f5f5;
            color: var(--dark-color);
            border: 1px solid #ddd;
        }

        .btn-secondary:hover {
            background-color: #e5e5e5;
        }

        .question-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .question-card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 20px;
            transition: var(--transition);
            display: flex;
            gap: 20px;
        }

        .question-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .question-stats {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 80px;
        }

        .stat {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 10px;
        }

        .stat-number {
            font-size: 20px;
            font-weight: 700;
            color: var(--primary-color);
        }

        .stat-label {
            font-size: 12px;
            color: #666;
        }

        .question-content {
            flex: 1;
        }

        .question-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .question-title a {
            color: var(--dark-color);
            text-decoration: none;
            transition: var(--transition);
        }

        .question-title a:hover {
            color: var(--primary-color);
        }

        .question-excerpt {
            color: #666;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .question-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: #888;
        }

        .question-author {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .question-time {
            font-size: 12px;
        }

        .empty-state {
            text-align: center;
            padding: 50px 0;
            color: #888;
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 20px;
            color: #ddd;
        }

        .empty-state h3 {
            font-size: 20px;
            margin-bottom: 10px;
        }

        footer {
            margin-top: 40px;
            padding: 20px 0;
            border-top: 1px solid #eee;
            text-align: center;
            color: #888;
            font-size: 14px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .question-card {
                flex-direction: column;
                gap: 10px;
            }

            .question-stats {
                flex-direction: row;
                justify-content: space-around;
                width: 100%;
            }

            .nav-links {
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <i class="fas fa-brain"></i>
                <h1>心理辅导护航平台</h1>
            </div>
            <div class="nav-links">
                <a href="/">聊天咨询</a>
                <a href="/qa" class="active">问答社区</a>
                <a href="/assessments">心理测评</a>
                {% if current_user.is_authenticated %}
                    {% if current_user.is_admin %}
                    <a href="/admin">管理后台</a>
                    {% endif %}
                    <div class="user-menu">
                        <div class="user-menu-button" id="user-menu-button">
                            <i class="fas fa-user-circle"></i>
                            {{ current_user.username }}
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="user-menu-dropdown" id="user-menu-dropdown">
                            <a href="{{ url_for('profile') }}" class="user-menu-item">
                                <i class="fas fa-user"></i> 个人资料
                            </a>
                            <a href="{{ url_for('logout') }}" class="user-menu-item danger">
                                <i class="fas fa-sign-out-alt"></i> 退出登录
                            </a>
                        </div>
                    </div>
                {% else %}
                    <a href="{{ url_for('login') }}">登录</a>
                    <a href="{{ url_for('register') }}">注册</a>
                {% endif %}
            </div>
        </header>

        <div class="main-content">
            <div class="page-header">
                <h2 class="page-title">心理问答社区</h2>
                <a href="/qa/ask" class="btn">
                    <i class="fas fa-plus"></i> 提出问题
                </a>
            </div>

            <div class="question-list">
                {% if questions %}
                    {% for question in questions %}
                        <div class="question-card">
                            <div class="question-stats">
                                <div class="stat">
                                    <div class="stat-number">{{ question.votes }}</div>
                                    <div class="stat-label">投票</div>
                                </div>
                                <div class="stat">
                                    <div class="stat-number">{{ question.answers|length }}</div>
                                    <div class="stat-label">回答</div>
                                </div>
                                <div class="stat">
                                    <div class="stat-number">{{ question.views }}</div>
                                    <div class="stat-label">浏览</div>
                                </div>
                            </div>
                            <div class="question-content">
                                <h3 class="question-title">
                                    <a href="/qa/question/{{ question.id }}">{{ question.title }}</a>
                                </h3>
                                <div class="question-excerpt">{{ question.content[:150] }}{% if question.content|length > 150 %}...{% endif %}</div>
                                <div class="question-meta">
                                    <div class="question-author">
                                        <i class="fas fa-user"></i> {{ question.author }}
                                    </div>
                                    <div class="question-time">
                                        提问于 {{ question.timestamp.split('T')[0] }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="empty-state">
                        <i class="fas fa-question-circle"></i>
                        <h3>暂无问题</h3>
                        <p>成为第一个提问的人吧！</p>
                        <a href="/qa/ask" class="btn" style="margin-top: 20px;">
                            <i class="fas fa-plus"></i> 提出问题
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <footer>
            <p>&copy; 2025 心理辅导护航平台 - 问答社区</p>
        </footer>
    </div>

    <script>
        // 用户菜单下拉
        const userMenuButton = document.getElementById('user-menu-button');
        const userMenuDropdown = document.getElementById('user-menu-dropdown');

        if (userMenuButton && userMenuDropdown) {
            userMenuButton.addEventListener('click', function() {
                userMenuDropdown.classList.toggle('show');
            });

            // 点击其他地方关闭下拉菜单
            document.addEventListener('click', function(event) {
                if (!userMenuButton.contains(event.target) && !userMenuDropdown.contains(event.target)) {
                    userMenuDropdown.classList.remove('show');
                }
            });
        }
    </script>
</body>
</html>

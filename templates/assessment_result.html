<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <title>测评结果 - 心理辅导护航平台</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- 引入字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- 引入图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #4e89ae;
            --secondary-color: #43658b;
            --light-color: #f5f5f5;
            --dark-color: #333;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --border-radius: 8px;
            --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            background-color: #f9f9f9;
            padding: 0;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            margin-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .logo i {
            font-size: 24px;
            color: var(--primary-color);
        }
        
        h1 {
            font-size: 24px;
            font-weight: 700;
            color: var(--primary-color);
            margin: 0;
        }
        
        .nav-links {
            display: flex;
            gap: 20px;
        }
        
        .nav-links a {
            color: var(--dark-color);
            text-decoration: none;
            padding: 5px 10px;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }
        
        .nav-links a:hover {
            background-color: var(--light-color);
        }
        
        .nav-links a.active {
            color: var(--primary-color);
            font-weight: 500;
        }
        
        .user-menu {
            position: relative;
        }
        
        .user-menu-button {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 5px 10px;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
        }
        
        .user-menu-button:hover {
            background-color: var(--light-color);
        }
        
        .user-menu-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            min-width: 180px;
            z-index: 100;
            display: none;
        }
        
        .user-menu-dropdown.show {
            display: block;
        }
        
        .user-menu-item {
            display: block;
            padding: 10px 15px;
            text-decoration: none;
            color: var(--dark-color);
            transition: var(--transition);
        }
        
        .user-menu-item:hover {
            background-color: var(--light-color);
        }
        
        .user-menu-item.danger {
            color: var(--danger-color);
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .breadcrumb a {
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .breadcrumb i {
            font-size: 12px;
            color: #888;
        }
        
        .result-card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow: hidden;
        }
        
        .result-header {
            padding: 20px;
            background-color: var(--primary-color);
            color: white;
        }
        
        .result-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .result-subtitle {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .result-body {
            padding: 30px;
        }
        
        .result-summary {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .result-score {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-weight: 700;
            color: white;
        }
        
        .score-normal {
            background-color: var(--success-color);
        }
        
        .score-mild {
            background-color: var(--warning-color);
        }
        
        .score-moderate {
            background-color: #d39e00;
        }
        
        .score-severe {
            background-color: var(--danger-color);
        }
        
        .score-number {
            font-size: 36px;
            line-height: 1;
        }
        
        .score-text {
            font-size: 16px;
            margin-top: 5px;
        }
        
        .result-info {
            flex: 1;
        }
        
        .result-label {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .result-description {
            color: #666;
            font-size: 16px;
            line-height: 1.6;
        }
        
        .result-details {
            margin-bottom: 30px;
        }
        
        .details-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .answers-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .answer-item {
            display: flex;
            gap: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f5f5f5;
        }
        
        .answer-question {
            flex: 1;
            font-weight: 500;
        }
        
        .answer-value {
            width: 80px;
            text-align: center;
            padding: 3px 8px;
            border-radius: 4px;
            background-color: var(--light-color);
        }
        
        .result-actions {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }
        
        .btn {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            padding: 12px 25px;
            border-radius: var(--border-radius);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            font-size: 16px;
        }
        
        .btn:hover {
            background-color: var(--secondary-color);
        }
        
        .btn-secondary {
            background-color: #f5f5f5;
            color: var(--dark-color);
            border: 1px solid #ddd;
        }
        
        .btn-secondary:hover {
            background-color: #e5e5e5;
        }
        
        footer {
            margin-top: 40px;
            padding: 20px 0;
            border-top: 1px solid #eee;
            text-align: center;
            color: #888;
            font-size: 14px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .result-summary {
                flex-direction: column;
                align-items: center;
                text-align: center;
            }
            
            .result-actions {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
                text-align: center;
            }
            
            .nav-links {
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <i class="fas fa-brain"></i>
                <h1>心理辅导护航平台</h1>
            </div>
            <div class="nav-links">
                <a href="/">聊天咨询</a>
                <a href="/qa">问答社区</a>
                <a href="/assessments" class="active">心理测评</a>
                {% if current_user.is_authenticated %}
                    {% if current_user.is_admin %}
                    <a href="/admin">管理后台</a>
                    {% endif %}
                    <div class="user-menu">
                        <div class="user-menu-button" id="user-menu-button">
                            <i class="fas fa-user-circle"></i>
                            {{ current_user.username }}
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="user-menu-dropdown" id="user-menu-dropdown">
                            <a href="{{ url_for('profile') }}" class="user-menu-item">
                                <i class="fas fa-user"></i> 个人资料
                            </a>
                            <a href="{{ url_for('logout') }}" class="user-menu-item danger">
                                <i class="fas fa-sign-out-alt"></i> 退出登录
                            </a>
                        </div>
                    </div>
                {% else %}
                    <a href="{{ url_for('login') }}">登录</a>
                    <a href="{{ url_for('register') }}">注册</a>
                {% endif %}
            </div>
        </header>
        
        <div class="main-content">
            <div class="breadcrumb">
                <a href="/assessments">心理测评</a>
                <i class="fas fa-chevron-right"></i>
                <a href="{{ url_for('assessment_detail', assessment_id=assessment.id) }}">{{ assessment.title }}</a>
                <i class="fas fa-chevron-right"></i>
                <span>测评结果</span>
            </div>
            
            <div class="result-card">
                <div class="result-header">
                    <h1 class="result-title">{{ assessment.title }} - 测评结果</h1>
                    <div class="result-subtitle">
                        测评时间: {{ result.timestamp.split('T')[0] }} {{ result.timestamp.split('T')[1].split('.')[0] }}
                    </div>
                </div>
                
                <div class="result-body">
                    <div class="result-summary">
                        <div class="result-score 
                            {% if result.result == '正常' %}score-normal
                            {% elif result.result == '轻度抑郁' or result.result == '轻度焦虑' %}score-mild
                            {% elif result.result == '中度抑郁' or result.result == '中度焦虑' %}score-moderate
                            {% elif result.result == '重度抑郁' or result.result == '重度焦虑' %}score-severe
                            {% endif %}">
                            <div class="score-number">{{ result.score }}</div>
                            <div class="score-text">{{ result.result }}</div>
                        </div>
                        
                        <div class="result-info">
                            <div class="result-label">测评结果解读</div>
                            <div class="result-description">
                                {{ result.description }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="result-details">
                        <h2 class="details-title">您的回答详情</h2>
                        <div class="answers-list">
                            {% for question in assessment.questions %}
                                <div class="answer-item">
                                    <div class="answer-question">
                                        {{ question.id }}. {{ question.text }}
                                    </div>
                                    <div class="answer-value">
                                        {% set answer_value = result.answers[question.id|string]|int %}
                                        {% for option in assessment.options %}
                                            {% if option.value == answer_value %}
                                                {{ option.text }}
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <div class="result-actions">
                        <a href="{{ url_for('assessment_detail', assessment_id=assessment.id) }}" class="btn btn-secondary">
                            返回测评详情
                        </a>
                        <a href="{{ url_for('assessment_take', assessment_id=assessment.id) }}" class="btn">
                            重新测评
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <footer>
            <p>&copy; 2025 心理辅导护航平台</p>
        </footer>
    </div>
    
    <script>
        // 用户菜单下拉
        const userMenuButton = document.getElementById('user-menu-button');
        const userMenuDropdown = document.getElementById('user-menu-dropdown');
        
        if (userMenuButton && userMenuDropdown) {
            userMenuButton.addEventListener('click', function() {
                userMenuDropdown.classList.toggle('show');
            });
            
            // 点击其他地方关闭下拉菜单
            document.addEventListener('click', function(event) {
                if (!userMenuButton.contains(event.target) && !userMenuDropdown.contains(event.target)) {
                    userMenuDropdown.classList.remove('show');
                }
            });
        }
    </script>
</body>
</html>

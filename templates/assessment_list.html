<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <title>心理测评 - 心理辅导护航平台</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- 引入字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- 引入图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #4e89ae;
            --secondary-color: #43658b;
            --light-color: #f5f5f5;
            --dark-color: #333;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --border-radius: 8px;
            --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            background-color: #f9f9f9;
            padding: 0;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            margin-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .logo i {
            font-size: 24px;
            color: var(--primary-color);
        }
        
        h1 {
            font-size: 24px;
            font-weight: 700;
            color: var(--primary-color);
            margin: 0;
        }
        
        .nav-links {
            display: flex;
            gap: 20px;
        }
        
        .nav-links a {
            color: var(--dark-color);
            text-decoration: none;
            padding: 5px 10px;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }
        
        .nav-links a:hover {
            background-color: var(--light-color);
        }
        
        .nav-links a.active {
            color: var(--primary-color);
            font-weight: 500;
        }
        
        .user-menu {
            position: relative;
        }
        
        .user-menu-button {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 5px 10px;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
        }
        
        .user-menu-button:hover {
            background-color: var(--light-color);
        }
        
        .user-menu-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            min-width: 180px;
            z-index: 100;
            display: none;
        }
        
        .user-menu-dropdown.show {
            display: block;
        }
        
        .user-menu-item {
            display: block;
            padding: 10px 15px;
            text-decoration: none;
            color: var(--dark-color);
            transition: var(--transition);
        }
        
        .user-menu-item:hover {
            background-color: var(--light-color);
        }
        
        .user-menu-item.danger {
            color: var(--danger-color);
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--dark-color);
        }
        
        .assessment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }
        
        .assessment-card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow: hidden;
            transition: var(--transition);
            display: flex;
            flex-direction: column;
        }
        
        .assessment-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }
        
        .assessment-header {
            padding: 20px;
            background-color: var(--primary-color);
            color: white;
        }
        
        .assessment-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .assessment-body {
            padding: 20px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .assessment-description {
            margin-bottom: 20px;
            color: #666;
            flex: 1;
        }
        
        .assessment-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: var(--border-radius);
            background-color: var(--light-color);
            margin-bottom: 15px;
        }
        
        .assessment-result-title {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .assessment-result-date {
            font-size: 12px;
            color: #888;
            margin-bottom: 5px;
        }
        
        .assessment-result-score {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 5px;
        }
        
        .assessment-result-label {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .result-normal {
            background-color: rgba(40, 167, 69, 0.1);
            color: var(--success-color);
        }
        
        .result-mild {
            background-color: rgba(255, 193, 7, 0.1);
            color: var(--warning-color);
        }
        
        .result-moderate {
            background-color: rgba(255, 193, 7, 0.2);
            color: #d39e00;
        }
        
        .result-severe {
            background-color: rgba(220, 53, 69, 0.1);
            color: var(--danger-color);
        }
        
        .btn {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            padding: 10px 20px;
            border-radius: var(--border-radius);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            font-size: 16px;
            text-align: center;
        }
        
        .btn:hover {
            background-color: var(--secondary-color);
        }
        
        .btn-block {
            display: block;
            width: 100%;
        }
        
        .btn-secondary {
            background-color: #f5f5f5;
            color: var(--dark-color);
            border: 1px solid #ddd;
        }
        
        .btn-secondary:hover {
            background-color: #e5e5e5;
        }
        
        .empty-state {
            text-align: center;
            padding: 50px 0;
            color: #888;
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: 20px;
            color: #ddd;
        }
        
        .empty-state h3 {
            font-size: 20px;
            margin-bottom: 10px;
        }
        
        footer {
            margin-top: 40px;
            padding: 20px 0;
            border-top: 1px solid #eee;
            text-align: center;
            color: #888;
            font-size: 14px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .assessment-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-links {
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <i class="fas fa-brain"></i>
                <h1>心理辅导护航平台</h1>
            </div>
            <div class="nav-links">
                <a href="/">聊天咨询</a>
                <a href="/qa">问答社区</a>
                <a href="/assessments" class="active">心理测评</a>
                {% if current_user.is_authenticated %}
                    {% if current_user.is_admin %}
                    <a href="/admin">管理后台</a>
                    {% endif %}
                    <div class="user-menu">
                        <div class="user-menu-button" id="user-menu-button">
                            <i class="fas fa-user-circle"></i>
                            {{ current_user.username }}
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="user-menu-dropdown" id="user-menu-dropdown">
                            <a href="{{ url_for('profile') }}" class="user-menu-item">
                                <i class="fas fa-user"></i> 个人资料
                            </a>
                            <a href="{{ url_for('logout') }}" class="user-menu-item danger">
                                <i class="fas fa-sign-out-alt"></i> 退出登录
                            </a>
                        </div>
                    </div>
                {% else %}
                    <a href="{{ url_for('login') }}">登录</a>
                    <a href="{{ url_for('register') }}">注册</a>
                {% endif %}
            </div>
        </header>
        
        <div class="main-content">
            <div class="page-header">
                <h2 class="page-title">心理测评</h2>
            </div>
            
            <div class="assessment-grid">
                {% if assessments %}
                    {% for assessment in assessments %}
                        <div class="assessment-card">
                            <div class="assessment-header">
                                <h3 class="assessment-title">{{ assessment.title }}</h3>
                            </div>
                            <div class="assessment-body">
                                <p class="assessment-description">{{ assessment.description }}</p>
                                
                                {% if assessment.user_result %}
                                    <div class="assessment-result">
                                        <div class="assessment-result-title">您的最近测评结果</div>
                                        <div class="assessment-result-date">
                                            测评时间: {{ assessment.user_result.timestamp.split('T')[0] }}
                                        </div>
                                        <div class="assessment-result-score">
                                            <span>得分: {{ assessment.user_result.score }}</span>
                                            <span class="assessment-result-label 
                                                {% if assessment.user_result.result == '正常' %}result-normal
                                                {% elif assessment.user_result.result == '轻度抑郁' or assessment.user_result.result == '轻度焦虑' %}result-mild
                                                {% elif assessment.user_result.result == '中度抑郁' or assessment.user_result.result == '中度焦虑' %}result-moderate
                                                {% elif assessment.user_result.result == '重度抑郁' or assessment.user_result.result == '重度焦虑' %}result-severe
                                                {% endif %}">
                                                {{ assessment.user_result.result }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="assessment-actions">
                                        <a href="{{ url_for('assessment_detail', assessment_id=assessment.id) }}" class="btn btn-secondary btn-block">
                                            查看详情
                                        </a>
                                        <a href="{{ url_for('assessment_take', assessment_id=assessment.id) }}" class="btn btn-block" style="margin-top: 10px;">
                                            重新测评
                                        </a>
                                    </div>
                                {% else %}
                                    <div class="assessment-actions">
                                        <a href="{{ url_for('assessment_take', assessment_id=assessment.id) }}" class="btn btn-block">
                                            开始测评
                                        </a>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="empty-state">
                        <i class="fas fa-clipboard-check"></i>
                        <h3>暂无可用的心理测评</h3>
                        <p>请稍后再来查看</p>
                    </div>
                {% endif %}
            </div>
        </div>
        
        <footer>
            <p>&copy; 2025 心理辅导护航平台</p>
        </footer>
    </div>
    
    <script>
        // 用户菜单下拉
        const userMenuButton = document.getElementById('user-menu-button');
        const userMenuDropdown = document.getElementById('user-menu-dropdown');
        
        if (userMenuButton && userMenuDropdown) {
            userMenuButton.addEventListener('click', function() {
                userMenuDropdown.classList.toggle('show');
            });
            
            // 点击其他地方关闭下拉菜单
            document.addEventListener('click', function(event) {
                if (!userMenuButton.contains(event.target) && !userMenuDropdown.contains(event.target)) {
                    userMenuDropdown.classList.remove('show');
                }
            });
        }
    </script>
</body>
</html>

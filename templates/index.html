<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <title>心理辅导护航平台</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="preconnect" href="https://cdn.jsdelivr.net">

    <!-- 引入字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- 引入图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- 延迟加载非关键资源 -->
    <script>
        // 延迟加载Markdown和代码高亮库
        window.addEventListener('load', function() {
            // 加载Marked.js
            if (!window.marked) {
                const markedScript = document.createElement('script');
                markedScript.src = 'https://cdn.jsdelivr.net/npm/marked/marked.min.js';
                markedScript.async = true;
                document.head.appendChild(markedScript);
            }

            // 加载highlight.js
            if (!window.hljs) {
                const hlCss = document.createElement('link');
                hlCss.rel = 'stylesheet';
                hlCss.href = 'https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/github.min.css';
                document.head.appendChild(hlCss);

                const hlScript = document.createElement('script');
                hlScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js';
                hlScript.async = true;
                document.head.appendChild(hlScript);
            }
        });
    </script>
    <style>
        :root {
            --primary-color: #4e89ae;
            --secondary-color: #43658b;
            --light-color: #f5f5f5;
            --dark-color: #333;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --border-radius: 8px;
            --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            background-color: #f9f9f9;
            padding: 0;
            margin: 0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            margin-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo i {
            font-size: 24px;
            color: var(--primary-color);
        }

        h1 {
            font-size: 24px;
            font-weight: 700;
            color: var(--primary-color);
            margin: 0;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: var(--dark-color);
            text-decoration: none;
            padding: 5px 10px;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .nav-links a:hover {
            background-color: var(--light-color);
        }

        .nav-links a.active {
            color: var(--primary-color);
            font-weight: 500;
        }

        .user-menu {
            position: relative;
        }

        .user-menu-button {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 5px 10px;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
        }

        .user-menu-button:hover {
            background-color: var(--light-color);
        }

        .user-menu-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            min-width: 180px;
            z-index: 100;
            display: none;
        }

        .user-menu-dropdown.show {
            display: block;
        }

        .user-menu-item {
            display: block;
            padding: 10px 15px;
            text-decoration: none;
            color: var(--dark-color);
            transition: var(--transition);
        }

        .user-menu-item:hover {
            background-color: var(--light-color);
        }

        .user-menu-item.danger {
            color: var(--danger-color);
        }

        .main-content {
            display: flex;
            flex: 1;
            gap: 20px;
        }

        .sidebar {
            width: 250px;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .chat-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow: hidden;
        }

        .chat-header {
            padding: 15px;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-title {
            font-size: 18px;
            font-weight: 500;
        }

        .chat-actions {
            display: flex;
            gap: 10px;
        }

        .chat-actions button {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 16px;
            transition: var(--transition);
        }

        .chat-actions button:hover {
            opacity: 0.8;
        }

        .chat-container {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background-color: #f9f9f9;
            min-height: 400px;
            scroll-behavior: smooth;
            will-change: scroll-position;
            -webkit-overflow-scrolling: touch;
        }

        .message {
            margin-bottom: 15px;
            max-width: 80%;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message-content {
            padding: 12px 15px;
            border-radius: var(--border-radius);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            line-height: 1.5;
        }

        .user {
            margin-left: auto;
            text-align: right;
        }

        .user .message-content {
            background-color: var(--primary-color);
            color: white;
            border-radius: var(--border-radius) var(--border-radius) 0 var(--border-radius);
        }

        .assistant {
            margin-right: auto;
        }

        .assistant .message-content {
            background-color: white;
            border: 1px solid #eee;
            border-radius: 0 var(--border-radius) var(--border-radius) var(--border-radius);
        }

        .message-time {
            font-size: 12px;
            color: #888;
            margin-top: 5px;
        }

        .typing-indicator {
            display: flex;
            padding: 12px 15px;
            background-color: white;
            border: 1px solid #eee;
            border-radius: 0 var(--border-radius) var(--border-radius) var(--border-radius);
            margin-bottom: 15px;
            width: fit-content;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background-color: #888;
            border-radius: 50%;
            margin: 0 2px;
            animation: typingAnimation 1.5s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: 0s; }
        .typing-dot:nth-child(2) { animation-delay: 0.3s; }
        .typing-dot:nth-child(3) { animation-delay: 0.6s; }

        @keyframes typingAnimation {
            0% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
            100% { transform: translateY(0); }
        }

        .input-area {
            padding: 15px;
            border-top: 1px solid #eee;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        #message-input {
            flex: 1;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-size: 16px;
            font-family: inherit;
            transition: var(--transition);
        }

        #message-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(78, 137, 174, 0.2);
        }

        .send-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            padding: 12px 20px;
            font-size: 16px;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .send-button:hover {
            background-color: var(--secondary-color);
        }

        .send-button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        .model-selector {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-size: 16px;
            width: 100%;
            margin-bottom: 10px;
        }

        .sidebar-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 10px;
            color: var(--primary-color);
        }

        .conversation-list {
            list-style: none;
            margin: 0;
            padding: 0;
            overflow-y: auto;
            flex: 1;
        }

        .conversation-item {
            padding: 10px;
            border-radius: var(--border-radius);
            margin-bottom: 8px;
            cursor: pointer;
            transition: var(--transition);
            border-left: 3px solid transparent;
        }

        .conversation-item:hover {
            background-color: #f5f5f5;
        }

        .conversation-item.active {
            background-color: #e6f7ff;
            border-left-color: var(--primary-color);
        }

        .conversation-title {
            font-weight: 500;
            margin-bottom: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .conversation-preview {
            font-size: 12px;
            color: #888;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .action-button {
            display: block;
            width: 100%;
            padding: 10px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
            text-align: center;
            font-size: 14px;
        }

        .action-button:hover {
            background-color: var(--secondary-color);
        }

        .action-button.secondary {
            background-color: #f5f5f5;
            color: var(--dark-color);
            border: 1px solid #ddd;
        }

        .action-button.secondary:hover {
            background-color: #e5e5e5;
        }

        .action-button.danger {
            background-color: var(--danger-color);
        }

        .action-button.danger:hover {
            background-color: #bd2130;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                margin-bottom: 20px;
            }

            .message {
                max-width: 90%;
            }
        }

        /* Markdown 样式 */
        .markdown-content {
            line-height: 1.6;
        }

        .markdown-content p {
            margin-bottom: 1em;
        }

        .markdown-content h1,
        .markdown-content h2,
        .markdown-content h3,
        .markdown-content h4 {
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }

        .markdown-content ul,
        .markdown-content ol {
            margin-bottom: 1em;
            padding-left: 2em;
        }

        .markdown-content blockquote {
            border-left: 4px solid var(--primary-color);
            padding-left: 1em;
            margin-left: 0;
            color: #666;
        }

        .markdown-content pre {
            background-color: #f6f8fa;
            border-radius: 3px;
            padding: 16px;
            overflow: auto;
            margin-bottom: 1em;
        }

        .markdown-content code {
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            background-color: #f6f8fa;
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-size: 85%;
        }

        .markdown-content pre code {
            background-color: transparent;
            padding: 0;
        }

        .hidden {
            display: none;
        }

        /* 错误和警告消息样式 */
        .error-message {
            background-color: #ffebee;
            border-left: 4px solid var(--danger-color);
            padding: 10px 15px;
            margin-bottom: 10px;
            border-radius: 4px;
        }

        .error-message i {
            color: var(--danger-color);
            margin-right: 5px;
        }

        .warning-message {
            background-color: #fff8e1;
            border-left: 4px solid var(--warning-color);
            padding: 10px 15px;
            margin-bottom: 10px;
            border-radius: 4px;
        }

        .warning-message i {
            color: var(--warning-color);
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <i class="fas fa-brain"></i>
                <h1>心理辅导护航平台</h1>
            </div>
            <div class="nav-links">
                <a href="/" class="active">聊天咨询</a>
                <a href="/qa">问答社区</a>
                <a href="/assessments">心理测评</a>
                {% if current_user.is_authenticated %}
                    {% if current_user.is_admin %}
                    <a href="/admin">管理后台</a>
                    {% endif %}
                    <div class="user-menu">
                        <div class="user-menu-button" id="user-menu-button">
                            <i class="fas fa-user-circle"></i>
                            {{ current_user.username }}
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="user-menu-dropdown" id="user-menu-dropdown">
                            <a href="{{ url_for('profile') }}" class="user-menu-item">
                                <i class="fas fa-user"></i> 个人资料
                            </a>
                            <a href="{{ url_for('logout') }}" class="user-menu-item danger">
                                <i class="fas fa-sign-out-alt"></i> 退出登录
                            </a>
                        </div>
                    </div>
                {% else %}
                    <a href="{{ url_for('login') }}">登录</a>
                    <a href="{{ url_for('register') }}">注册</a>
                {% endif %}
            </div>
        </header>

        <div class="main-content">
            <div class="sidebar">
                <div class="sidebar-title">设置</div>
                <select id="model-selector" class="model-selector">
                    {% for model_id, model_name in models.items() %}
                    <option value="{{ model_id }}">{{ model_name }}</option>
                    {% endfor %}
                </select>

                <button id="new-chat-btn" class="action-button">
                    <i class="fas fa-plus"></i> 新对话
                </button>

                <div class="sidebar-title" style="margin-top: 20px;">历史对话</div>
                <ul id="conversation-list" class="conversation-list">
                    <!-- 历史对话将在这里动态加载 -->
                </ul>
            </div>

            <div class="chat-panel">
                <div class="chat-header">
                    <div class="chat-title">新对话</div>
                    <div class="chat-actions">
                        <button id="clear-chat-btn" title="清空对话">
                            <i class="fas fa-trash-alt"></i>
                        </button>
                    </div>
                </div>

                <div id="chat-container" class="chat-container">
                    <!-- 消息将在这里动态加载 -->
                </div>

                <div class="input-area">
                    <textarea id="message-input" placeholder="请输入您的问题..." rows="1"></textarea>
                    <button id="send-button" class="send-button" onclick="sendMessage()">
                        <i class="fas fa-paper-plane"></i> 发送
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let chatHistory = [];
        let conversationId = null;
        let currentModel = 'deepseek-chat';
        let isTyping = false;

        // 工具函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        function throttle(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            }
        }

        // DOM元素
        const chatContainer = document.getElementById('chat-container');
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        const modelSelector = document.getElementById('model-selector');
        const newChatBtn = document.getElementById('new-chat-btn');
        const clearChatBtn = document.getElementById('clear-chat-btn');
        const conversationList = document.getElementById('conversation-list');

        // 自动调整文本框高度
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
            if (this.scrollHeight > 200) {
                this.style.height = '200px';
                this.style.overflowY = 'auto';
            } else {
                this.style.overflowY = 'hidden';
            }
        });

        // 按Enter键发送消息（Shift+Enter换行）
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // 模型选择变更
        modelSelector.addEventListener('change', function() {
            currentModel = this.value;
        });

        // 新对话按钮
        newChatBtn.addEventListener('click', function() {
            startNewConversation();
        });

        // 清空对话按钮
        clearChatBtn.addEventListener('click', function() {
            if (confirm('确定要清空当前对话吗？')) {
                chatContainer.innerHTML = '';
                chatHistory = [];
                addMessage('您好！我是您的心理辅导助手。请告诉我您今天想聊些什么？', false);
            }
        });

        // 格式化时间
        function formatTime(date) {
            return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        }

        // 添加消息到聊天界面
        function addMessage(content, isUser, withAnimation = true) {
            if (!content || !chatContainer) return;

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user' : 'assistant'}`;

            const now = new Date();
            const timeStr = formatTime(now);

            // 创建消息内容元素
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';

            try {
                if (!isUser) {
                    // 使用Markdown渲染助手消息
                    contentDiv.classList.add('markdown-content');

                    // 使用requestAnimationFrame来优化渲染性能
                    requestAnimationFrame(() => {
                        try {
                            contentDiv.innerHTML = marked.parse(content);

                            // 代码高亮 - 异步处理
                            setTimeout(() => {
                                contentDiv.querySelectorAll('pre code').forEach((block) => {
                                    try {
                                        hljs.highlightElement(block);
                                    } catch (e) {
                                        console.warn('Code highlighting failed:', e);
                                    }
                                });
                            }, 0);
                        } catch (e) {
                            console.error('Markdown parsing failed:', e);
                            contentDiv.textContent = content; // 降级到纯文本
                        }
                    });
                } else {
                    // 用户消息不使用Markdown
                    contentDiv.textContent = content;
                }
            } catch (e) {
                console.error('Message rendering failed:', e);
                contentDiv.textContent = content; // 降级到纯文本
            }

            // 创建时间元素
            const timeDiv = document.createElement('div');
            timeDiv.className = 'message-time';
            timeDiv.textContent = timeStr;

            // 添加到消息div
            messageDiv.appendChild(contentDiv);
            messageDiv.appendChild(timeDiv);

            // 添加到聊天容器
            chatContainer.appendChild(messageDiv);

            // 平滑滚动到底部
            requestAnimationFrame(() => {
                chatContainer.scrollTop = chatContainer.scrollHeight;
            });

            // 更新聊天历史
            try {
                if (isUser) {
                    chatHistory.push({"role": "user", "content": content});
                } else {
                    chatHistory.push({"role": "assistant", "content": content});
                }

                // 限制历史记录长度以提高性能
                if (chatHistory.length > 50) {
                    chatHistory = chatHistory.slice(-40); // 保留最近40条消息
                }
            } catch (e) {
                console.error('Failed to update chat history:', e);
            }
        }

        // 显示正在输入指示器
        function showTypingIndicator() {
            const typingDiv = document.createElement('div');
            typingDiv.className = 'message assistant';
            typingDiv.id = 'typing-indicator';

            const indicatorDiv = document.createElement('div');
            indicatorDiv.className = 'typing-indicator';

            for (let i = 0; i < 3; i++) {
                const dot = document.createElement('div');
                dot.className = 'typing-dot';
                indicatorDiv.appendChild(dot);
            }

            typingDiv.appendChild(indicatorDiv);
            chatContainer.appendChild(typingDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // 隐藏正在输入指示器
        function hideTypingIndicator() {
            const indicator = document.getElementById('typing-indicator');
            if (indicator) {
                indicator.remove();
            }
        }

        // 发送消息
        function sendMessage() {
            const message = messageInput.value.trim();

            if (!message || isTyping) {
                return;
            }

            // 禁用输入和发送按钮
            isTyping = true;
            messageInput.disabled = true;
            sendButton.disabled = true;
            sendButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 发送中...';

            // 添加用户消息
            addMessage(message, true);
            messageInput.value = '';
            messageInput.style.height = 'auto';

            // 显示正在输入指示器
            showTypingIndicator();

            // 创建请求数据
            const requestData = {
                message: message,
                history: chatHistory.slice(-10), // 只发送最近10条消息以提高速度
                model: currentModel,
                conversation_id: conversationId
            };

            // 发送API请求
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

            fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData),
                signal: controller.signal
            })
            .then(response => {
                clearTimeout(timeoutId);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // 隐藏正在输入指示器
                hideTypingIndicator();

                if (data.error) {
                    // 显示错误消息
                    const errorMsg = `❌ **错误:** ${data.error}${data.details ? `\n\n*详情: ${data.details}*` : ''}`;
                    addMessage(errorMsg, false);

                    // 如果有可用模型列表，显示它们
                    if (data.available_models) {
                        const modelsList = data.available_models.join(', ');
                        addMessage(`💡 **可用的模型有:** ${modelsList}`, false);
                    }
                } else {
                    // 保存会话ID
                    if (data.conversation_id) {
                        conversationId = data.conversation_id;
                    }

                    // 如果有警告，显示警告消息
                    if (data.warning) {
                        const warningMsg = `⚠️ **提示:** ${data.warning}`;
                        addMessage(warningMsg, false);

                        // 更新模型选择器
                        if (data.model && modelSelector) {
                            currentModel = data.model;
                            modelSelector.value = data.model;
                        }
                    }

                    // 添加助手回复
                    if (data.reply) {
                        addMessage(data.reply, false);
                    }

                    // 使用防抖延迟更新历史对话列表
                    debouncedLoadConversations();
                }
            })
            .catch(error => {
                clearTimeout(timeoutId);
                console.error('Error:', error);
                hideTypingIndicator();

                let errorMessage = '抱歉，发生了错误，请稍后再试。';
                if (error.name === 'AbortError') {
                    errorMessage = '请求超时，请检查网络连接后重试。';
                } else if (error.message.includes('HTTP error')) {
                    errorMessage = '服务器响应错误，请稍后重试。';
                }

                addMessage(`❌ ${errorMessage}`, false);
            })
            .finally(() => {
                // 重新启用输入和发送按钮
                isTyping = false;
                messageInput.disabled = false;
                sendButton.disabled = false;
                sendButton.innerHTML = '<i class="fas fa-paper-plane"></i> 发送';
                messageInput.focus();
            });
        }

        // 防抖的对话列表加载函数
        const debouncedLoadConversations = debounce(loadConversations, 500);

        // 加载历史对话列表
        function loadConversations() {
            fetch('/api/conversations')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    if (!conversationList) return;

                    conversationList.innerHTML = '';

                    if (!data.conversations || data.conversations.length === 0) {
                        const emptyItem = document.createElement('li');
                        emptyItem.textContent = '暂无历史对话';
                        emptyItem.style.padding = '10px';
                        emptyItem.style.color = '#888';
                        conversationList.appendChild(emptyItem);
                        return;
                    }

                    data.conversations.forEach(conv => {
                        if (!conv.id) return; // 跳过无效的对话

                        const item = document.createElement('li');
                        item.className = 'conversation-item';
                        item.dataset.id = conv.id; // 设置数据属性

                        if (conv.id === conversationId) {
                            item.classList.add('active');
                        }

                        const title = document.createElement('div');
                        title.className = 'conversation-title';

                        // 格式化日期
                        try {
                            const date = new Date(conv.timestamp);
                            const formattedDate = date.toLocaleDateString('zh-CN', {
                                month: 'short',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                            });
                            title.textContent = formattedDate;
                        } catch (e) {
                            title.textContent = '未知时间';
                        }

                        const preview = document.createElement('div');
                        preview.className = 'conversation-preview';
                        preview.textContent = conv.preview || '空对话';

                        item.appendChild(title);
                        item.appendChild(preview);

                        // 点击加载对话
                        item.addEventListener('click', () => loadConversation(conv.id));

                        conversationList.appendChild(item);
                    });
                })
                .catch(error => {
                    console.error('Error loading conversations:', error);
                    if (conversationList) {
                        conversationList.innerHTML = '<li style="padding: 10px; color: #888;">加载对话失败</li>';
                    }
                });
        }

        // 加载特定对话
        function loadConversation(id) {
            if (!id) return;

            // 显示加载状态
            if (chatContainer) {
                chatContainer.innerHTML = '<div style="text-align: center; padding: 20px; color: #888;">加载对话中...</div>';
            }

            fetch(`/api/conversations/${id}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to load conversation');
                    }
                    return response.json();
                })
                .then(data => {
                    if (!chatContainer) return;

                    // 更新UI
                    chatContainer.innerHTML = '';
                    chatHistory = [];
                    conversationId = id;

                    // 更新活动状态
                    document.querySelectorAll('.conversation-item').forEach(item => {
                        item.classList.remove('active');
                    });

                    // 找到并激活当前对话项
                    document.querySelectorAll('.conversation-item').forEach(item => {
                        if (item.dataset && item.dataset.id === id) {
                            item.classList.add('active');
                        }
                    });

                    // 显示消息
                    if (data.messages && Array.isArray(data.messages)) {
                        data.messages.forEach(msg => {
                            if (msg && msg.content && msg.role) {
                                addMessage(msg.content, msg.role === 'user', false);
                            }
                        });
                    }

                    // 如果没有消息，显示欢迎消息
                    if (!data.messages || data.messages.length === 0) {
                        addMessage('您好！我是您的心理辅导助手。请告诉我您今天想聊些什么？', false, false);
                    }
                })
                .catch(error => {
                    console.error('Error loading conversation:', error);
                    if (chatContainer) {
                        chatContainer.innerHTML = '<div style="text-align: center; padding: 20px; color: #dc3545;">加载对话失败，请稍后重试</div>';
                    }
                });
        }

        // 开始新对话
        function startNewConversation() {
            chatContainer.innerHTML = '';
            chatHistory = [];
            conversationId = null;

            // 移除活动状态
            document.querySelectorAll('.conversation-item').forEach(item => {
                item.classList.remove('active');
            });

            // 显示欢迎消息
            addMessage('您好！我是您的心理辅导助手。请告诉我您今天想聊些什么？', false);
        }

        // 用户菜单下拉
        function setupUserMenu() {
            const userMenuButton = document.getElementById('user-menu-button');
            const userMenuDropdown = document.getElementById('user-menu-dropdown');

            if (userMenuButton && userMenuDropdown) {
                userMenuButton.addEventListener('click', function() {
                    userMenuDropdown.classList.toggle('show');
                });

                // 点击其他地方关闭下拉菜单
                document.addEventListener('click', function(event) {
                    if (!userMenuButton.contains(event.target) && !userMenuDropdown.contains(event.target)) {
                        userMenuDropdown.classList.remove('show');
                    }
                });
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 加载历史对话
            loadConversations();

            // 显示欢迎消息
            addMessage('您好！我是您的心理辅导助手。请告诉我您今天想聊些什么？', false);

            // 设置用户菜单
            setupUserMenu();
        });
    </script>
</body>
</html>
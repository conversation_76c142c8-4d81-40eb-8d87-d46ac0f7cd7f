<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <title>提出问题 - 心理辅导护航平台</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- 引入字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- 引入图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #4e89ae;
            --secondary-color: #43658b;
            --light-color: #f5f5f5;
            --dark-color: #333;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --border-radius: 8px;
            --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            background-color: #f9f9f9;
            padding: 0;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            margin-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .logo i {
            font-size: 24px;
            color: var(--primary-color);
        }
        
        h1 {
            font-size: 24px;
            font-weight: 700;
            color: var(--primary-color);
            margin: 0;
        }
        
        .nav-links {
            display: flex;
            gap: 20px;
        }
        
        .nav-links a {
            color: var(--dark-color);
            text-decoration: none;
            padding: 5px 10px;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }
        
        .nav-links a:hover {
            background-color: var(--light-color);
        }
        
        .nav-links a.active {
            color: var(--primary-color);
            font-weight: 500;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .breadcrumb a {
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .breadcrumb i {
            font-size: 12px;
            color: #888;
        }
        
        .page-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 20px;
        }
        
        .ask-form {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 30px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-size: 16px;
            font-family: inherit;
            transition: var(--transition);
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(78, 137, 174, 0.2);
        }
        
        textarea.form-control {
            min-height: 200px;
            resize: vertical;
        }
        
        .form-text {
            margin-top: 5px;
            font-size: 14px;
            color: #888;
        }
        
        .btn {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            padding: 12px 25px;
            border-radius: var(--border-radius);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            font-size: 16px;
        }
        
        .btn:hover {
            background-color: var(--secondary-color);
        }
        
        .btn-secondary {
            background-color: #f5f5f5;
            color: var(--dark-color);
            border: 1px solid #ddd;
            margin-right: 10px;
        }
        
        .btn-secondary:hover {
            background-color: #e5e5e5;
        }
        
        .form-actions {
            display: flex;
            justify-content: flex-end;
        }
        
        .alert {
            padding: 15px;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
            border-left: 4px solid;
        }
        
        .alert-danger {
            background-color: #ffebee;
            border-color: var(--danger-color);
            color: #d32f2f;
        }
        
        footer {
            margin-top: 40px;
            padding: 20px 0;
            border-top: 1px solid #eee;
            text-align: center;
            color: #888;
            font-size: 14px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .form-actions {
                flex-direction: column;
                gap: 10px;
            }
            
            .btn-secondary {
                margin-right: 0;
                margin-bottom: 10px;
            }
            
            .nav-links {
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <i class="fas fa-brain"></i>
                <h1>心理辅导护航平台</h1>
            </div>
            <div class="nav-links">
                <a href="/">聊天咨询</a>
                <a href="/qa" class="active">问答社区</a>
            </div>
        </header>
        
        <div class="main-content">
            <div class="breadcrumb">
                <a href="/qa">问答社区</a>
                <i class="fas fa-chevron-right"></i>
                <span>提出问题</span>
            </div>
            
            <h1 class="page-title">提出问题</h1>
            
            {% if error %}
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> {{ error }}
                </div>
            {% endif %}
            
            <div class="ask-form">
                <form method="post" action="/qa/ask">
                    <div class="form-group">
                        <label for="title" class="form-label">问题标题</label>
                        <input type="text" id="title" name="title" class="form-control" placeholder="请用一句话描述您的问题" required>
                        <div class="form-text">一个好的标题能够吸引更多人关注您的问题</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="content" class="form-label">问题详情</label>
                        <textarea id="content" name="content" class="form-control" placeholder="请详细描述您的问题，包括背景、症状、您已经尝试过的方法等" required></textarea>
                        <div class="form-text">详细的描述有助于他人更好地理解您的问题并提供有针对性的回答</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="author" class="form-label">您的名字</label>
                        <input type="text" id="author" name="author" class="form-control" placeholder="匿名用户">
                        <div class="form-text">可选，如不填写将显示为"匿名用户"</div>
                    </div>
                    
                    <div class="form-actions">
                        <a href="/qa" class="btn btn-secondary">取消</a>
                        <button type="submit" class="btn">提交问题</button>
                    </div>
                </form>
            </div>
        </div>
        
        <footer>
            <p>&copy; 2025 心理辅导护航平台 - 问答社区</p>
        </footer>
    </div>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <title>{{ question.title }} - 心理辅导护航平台</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- 引入字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- 引入图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 引入Markdown渲染库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <!-- 引入代码高亮 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/github.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <style>
        :root {
            --primary-color: #4e89ae;
            --secondary-color: #43658b;
            --light-color: #f5f5f5;
            --dark-color: #333;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --border-radius: 8px;
            --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            background-color: #f9f9f9;
            padding: 0;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            margin-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .logo i {
            font-size: 24px;
            color: var(--primary-color);
        }
        
        h1 {
            font-size: 24px;
            font-weight: 700;
            color: var(--primary-color);
            margin: 0;
        }
        
        .nav-links {
            display: flex;
            gap: 20px;
        }
        
        .nav-links a {
            color: var(--dark-color);
            text-decoration: none;
            padding: 5px 10px;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }
        
        .nav-links a:hover {
            background-color: var(--light-color);
        }
        
        .nav-links a.active {
            color: var(--primary-color);
            font-weight: 500;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .breadcrumb a {
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .breadcrumb i {
            font-size: 12px;
            color: #888;
        }
        
        .question-header {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .question-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 15px;
            color: var(--dark-color);
        }
        
        .question-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            font-size: 14px;
            color: #888;
        }
        
        .question-author {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .question-stats {
            display: flex;
            gap: 15px;
        }
        
        .stat {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .question-content {
            line-height: 1.8;
            margin-bottom: 20px;
        }
        
        .vote-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .vote-btn {
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 8px 15px;
            border-radius: var(--border-radius);
            border: 1px solid #ddd;
            background-color: white;
            cursor: pointer;
            transition: var(--transition);
        }
        
        .vote-btn:hover {
            background-color: var(--light-color);
        }
        
        .vote-btn.upvote:hover {
            color: var(--success-color);
            border-color: var(--success-color);
        }
        
        .vote-btn.downvote:hover {
            color: var(--danger-color);
            border-color: var(--danger-color);
        }
        
        .answers-section {
            margin-top: 30px;
        }
        
        .answers-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .answers-title {
            font-size: 20px;
            font-weight: 600;
        }
        
        .answer-count {
            color: #888;
            font-size: 16px;
        }
        
        .answer-list {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .answer-card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 20px;
        }
        
        .answer-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            font-size: 14px;
            color: #888;
        }
        
        .answer-author {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .answer-content {
            line-height: 1.8;
            margin-bottom: 20px;
        }
        
        .new-answer-form {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 20px;
            margin-top: 30px;
        }
        
        .form-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-size: 16px;
            font-family: inherit;
            transition: var(--transition);
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(78, 137, 174, 0.2);
        }
        
        textarea.form-control {
            min-height: 150px;
            resize: vertical;
        }
        
        .btn {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            padding: 10px 20px;
            border-radius: var(--border-radius);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            font-size: 16px;
        }
        
        .btn:hover {
            background-color: var(--secondary-color);
        }
        
        .btn-secondary {
            background-color: #f5f5f5;
            color: var(--dark-color);
            border: 1px solid #ddd;
        }
        
        .btn-secondary:hover {
            background-color: #e5e5e5;
        }
        
        footer {
            margin-top: 40px;
            padding: 20px 0;
            border-top: 1px solid #eee;
            text-align: center;
            color: #888;
            font-size: 14px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .question-meta, .answer-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .nav-links {
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <i class="fas fa-brain"></i>
                <h1>心理辅导护航平台</h1>
            </div>
            <div class="nav-links">
                <a href="/">聊天咨询</a>
                <a href="/qa" class="active">问答社区</a>
            </div>
        </header>
        
        <div class="main-content">
            <div class="breadcrumb">
                <a href="/qa">问答社区</a>
                <i class="fas fa-chevron-right"></i>
                <span>问题详情</span>
            </div>
            
            <div class="question-header">
                <h1 class="question-title">{{ question.title }}</h1>
                <div class="question-meta">
                    <div class="question-author">
                        <i class="fas fa-user"></i> {{ question.author }}
                        <span style="margin-left: 15px;">提问于 {{ question.timestamp.split('T')[0] }}</span>
                    </div>
                    <div class="question-stats">
                        <div class="stat">
                            <i class="fas fa-eye"></i> {{ question.views }} 浏览
                        </div>
                        <div class="stat">
                            <i class="fas fa-comment"></i> {{ question.answers|length }} 回答
                        </div>
                        <div class="stat">
                            <i class="fas fa-thumbs-up"></i> {{ question.votes }} 投票
                        </div>
                    </div>
                </div>
                <div class="question-content">
                    {{ question.content }}
                </div>
                <div class="vote-buttons">
                    <button class="vote-btn upvote" onclick="voteQuestion('{{ question.id }}', 'up')">
                        <i class="fas fa-thumbs-up"></i> 赞同
                    </button>
                    <button class="vote-btn downvote" onclick="voteQuestion('{{ question.id }}', 'down')">
                        <i class="fas fa-thumbs-down"></i> 不赞同
                    </button>
                </div>
            </div>
            
            <div class="answers-section">
                <div class="answers-header">
                    <h2 class="answers-title">回答</h2>
                    <div class="answer-count">{{ question.answers|length }} 个回答</div>
                </div>
                
                <div class="answer-list">
                    {% if question.answers %}
                        {% for answer in question.answers %}
                            <div class="answer-card" id="answer-{{ answer.id }}">
                                <div class="answer-meta">
                                    <div class="answer-author">
                                        <i class="fas fa-user"></i> {{ answer.author }}
                                        <span style="margin-left: 15px;">回答于 {{ answer.timestamp.split('T')[0] }}</span>
                                    </div>
                                    <div class="answer-votes">
                                        <i class="fas fa-thumbs-up"></i> {{ answer.votes }} 投票
                                    </div>
                                </div>
                                <div class="answer-content">
                                    {{ answer.content }}
                                </div>
                                <div class="vote-buttons">
                                    <button class="vote-btn upvote" onclick="voteAnswer('{{ question.id }}', '{{ answer.id }}', 'up')">
                                        <i class="fas fa-thumbs-up"></i> 赞同
                                    </button>
                                    <button class="vote-btn downvote" onclick="voteAnswer('{{ question.id }}', '{{ answer.id }}', 'down')">
                                        <i class="fas fa-thumbs-down"></i> 不赞同
                                    </button>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div style="text-align: center; padding: 30px; color: #888;">
                            <i class="fas fa-comment-slash" style="font-size: 48px; margin-bottom: 20px;"></i>
                            <h3>暂无回答</h3>
                            <p>成为第一个回答这个问题的人吧！</p>
                        </div>
                    {% endif %}
                </div>
                
                <div class="new-answer-form">
                    <h3 class="form-title">添加回答</h3>
                    <form id="answer-form">
                        <div class="form-group">
                            <label for="answer-content" class="form-label">您的回答</label>
                            <textarea id="answer-content" class="form-control" rows="6" placeholder="请输入您的回答..."></textarea>
                        </div>
                        <div class="form-group">
                            <label for="answer-author" class="form-label">您的名字</label>
                            <input type="text" id="answer-author" class="form-control" placeholder="匿名用户">
                        </div>
                        <button type="submit" class="btn">提交回答</button>
                    </form>
                </div>
            </div>
        </div>
        
        <footer>
            <p>&copy; 2025 心理辅导护航平台 - 问答社区</p>
        </footer>
    </div>

    <script>
        // 提交回答
        document.getElementById('answer-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const content = document.getElementById('answer-content').value.trim();
            const author = document.getElementById('answer-author').value.trim() || '匿名用户';
            
            if (!content) {
                alert('回答内容不能为空');
                return;
            }
            
            fetch(`/api/qa/questions/{{ question.id }}/answers`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    content: content,
                    author: author
                }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 刷新页面显示新回答
                    window.location.reload();
                } else {
                    alert('提交回答失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('提交回答时发生错误，请稍后再试');
            });
        });
        
        // 问题投票
        function voteQuestion(questionId, voteType) {
            fetch(`/api/qa/questions/${questionId}/vote`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    vote_type: voteType
                }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新投票数显示
                    document.querySelector('.question-stats .stat:nth-child(3)').innerHTML = 
                        `<i class="fas fa-thumbs-up"></i> ${data.votes} 投票`;
                } else {
                    alert('投票失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('投票时发生错误，请稍后再试');
            });
        }
        
        // 回答投票
        function voteAnswer(questionId, answerId, voteType) {
            fetch(`/api/qa/questions/${questionId}/answers/${answerId}/vote`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    vote_type: voteType
                }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新投票数显示
                    const answerCard = document.getElementById(`answer-${answerId}`);
                    answerCard.querySelector('.answer-votes').innerHTML = 
                        `<i class="fas fa-thumbs-up"></i> ${data.votes} 投票`;
                } else {
                    alert('投票失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('投票时发生错误，请稍后再试');
            });
        }
    </script>
</body>
</html>

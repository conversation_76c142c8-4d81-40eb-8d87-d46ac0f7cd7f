from flask import Flask, request, jsonify, render_template, session, redirect, url_for, flash
from flask_login import <PERSON>gin<PERSON>ana<PERSON>, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from openai import OpenAI
import json
import os
from datetime import datetime, timedelta
import uuid
import re
import functools

app = Flask(__name__)
app.secret_key = os.environ.get('FLASK_SECRET_KEY', 'dev_secret_key')
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(days=7)

# 初始化Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = '请先登录以访问此页面'

# 用户数据存储
USERS_FILE = 'users.json'

# 确保用户文件存在
if not os.path.exists(USERS_FILE):
    with open(USERS_FILE, 'w', encoding='utf-8') as f:
        json.dump([], f, ensure_ascii=False)

# 用户模型
class User(UserMixin):
    def __init__(self, id, username, email, password_hash, is_admin=False):
        self.id = id
        self.username = username
        self.email = email
        self.password_hash = password_hash
        self.is_admin = is_admin

    @staticmethod
    def get(user_id):
        users = load_users()
        for user in users:
            if user['id'] == user_id:
                return User(
                    id=user['id'],
                    username=user['username'],
                    email=user['email'],
                    password_hash=user['password_hash'],
                    is_admin=user.get('is_admin', False)
                )
        return None

    @staticmethod
    def find_by_email(email):
        users = load_users()
        for user in users:
            if user['email'] == email:
                return User(
                    id=user['id'],
                    username=user['username'],
                    email=user['email'],
                    password_hash=user['password_hash'],
                    is_admin=user.get('is_admin', False)
                )
        return None

    @staticmethod
    def find_by_username(username):
        users = load_users()
        for user in users:
            if user['username'] == username:
                return User(
                    id=user['id'],
                    username=user['username'],
                    email=user['email'],
                    password_hash=user['password_hash'],
                    is_admin=user.get('is_admin', False)
                )
        return None

# 用户加载函数
@login_manager.user_loader
def load_user(user_id):
    return User.get(user_id)

# 管理员权限检查装饰器
def admin_required(func):
    @functools.wraps(func)
    def decorated_view(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            flash('您没有权限访问此页面', 'danger')
            return redirect(url_for('index'))
        return func(*args, **kwargs)
    return decorated_view

# 用户数据操作函数
def load_users():
    """加载所有用户"""
    if os.path.exists(USERS_FILE):
        with open(USERS_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    return []

def save_users(users):
    """保存所有用户"""
    with open(USERS_FILE, 'w', encoding='utf-8') as f:
        json.dump(users, f, ensure_ascii=False, indent=2)

def create_user(username, email, password, is_admin=False):
    """创建新用户"""
    users = load_users()

    # 检查邮箱是否已存在
    if any(user['email'] == email for user in users):
        return False, "邮箱已被注册"

    # 检查用户名是否已存在
    if any(user['username'] == username for user in users):
        return False, "用户名已被使用"

    # 创建新用户
    new_user = {
        'id': str(uuid.uuid4()),
        'username': username,
        'email': email,
        'password_hash': generate_password_hash(password),
        'is_admin': is_admin,
        'created_at': datetime.now().isoformat()
    }

    users.append(new_user)
    save_users(users)
    return True, "用户创建成功"

# 初始化DeepSeek API客户端
client = OpenAI(
    api_key="***********************************",
    base_url="https://api.deepseek.com"
)

# 可用模型列表
AVAILABLE_MODELS = {
    "deepseek-chat": "DeepSeek Chat",
    "deepseek-coder": "DeepSeek Coder"
}

# 系统提示词 - 心理咨询场景
DEFAULT_SYSTEM_MESSAGE = """你是一位专业的心理咨询师，名为"心灵导航"。
你擅长倾听、理解用户的心理困扰，并提供专业、温暖的支持和建议。
请注意以下几点：
1. 保持专业、耐心和同理心，理解用户的情感需求
2. 提供有建设性的建议，但不要过度承诺或保证解决所有问题
3. 如遇到严重心理健康问题，建议用户寻求专业医疗帮助
4. 回答应当简洁明了，易于理解，避免过长的专业术语解释
5. 保持积极向上的态度，鼓励用户面对困难
"""

# 会话历史记录存储
CONVERSATIONS_DIR = "conversations"
os.makedirs(CONVERSATIONS_DIR, exist_ok=True)

# 问答模块数据存储
QA_DIR = "qa_data"
QUESTIONS_FILE = os.path.join(QA_DIR, "questions.json")
os.makedirs(QA_DIR, exist_ok=True)

# 初始化问答数据文件
if not os.path.exists(QUESTIONS_FILE):
    with open(QUESTIONS_FILE, 'w', encoding='utf-8') as f:
        json.dump([], f, ensure_ascii=False, indent=2)

# 心理测评模块数据存储
ASSESSMENT_DIR = "assessment_data"
ASSESSMENTS_FILE = os.path.join(ASSESSMENT_DIR, "assessments.json")
RESULTS_FILE = os.path.join(ASSESSMENT_DIR, "results.json")
os.makedirs(ASSESSMENT_DIR, exist_ok=True)

# 初始化心理测评数据文件
if not os.path.exists(ASSESSMENTS_FILE):
    # 创建一些默认的心理测评
    default_assessments = [
        {
            "id": "sds",
            "title": "抑郁自评量表(SDS)",
            "description": "抑郁自评量表(Self-Rating Depression Scale, SDS)由Zung于1965年编制，用于评定有抑郁症状的患者的主观感受及症状严重程度。",
            "instructions": "请仔细阅读每一条目，根据您最近一周的实际感受选择最符合的选项。",
            "questions": [
                {"id": 1, "text": "我感到情绪沮丧，郁闷"},
                {"id": 2, "text": "我感到早晨心情最好"},
                {"id": 3, "text": "我要哭或想哭"},
                {"id": 4, "text": "我晚上睡眠不好"},
                {"id": 5, "text": "我吃得跟平常一样多"},
                {"id": 6, "text": "我与异性接触时仍感到愉快"},
                {"id": 7, "text": "我发现自己在消瘦"},
                {"id": 8, "text": "我有便秘的苦恼"},
                {"id": 9, "text": "我的心跳比平时快"},
                {"id": 10, "text": "我无缘无故感到疲劳"},
                {"id": 11, "text": "我的头脑跟平常一样清楚"},
                {"id": 12, "text": "我做事像平时一样不感到困难"},
                {"id": 13, "text": "我坐卧不安，难以保持平静"},
                {"id": 14, "text": "我对未来抱有希望"},
                {"id": 15, "text": "我比平常更容易激动"},
                {"id": 16, "text": "我觉得决定什么事很容易"},
                {"id": 17, "text": "我感到自己是有用的和不可缺少的人"},
                {"id": 18, "text": "我的生活很有意义"},
                {"id": 19, "text": "假若我死了别人会过得更好"},
                {"id": 20, "text": "我仍旧喜爱自己平时喜爱的东西"}
            ],
            "options": [
                {"value": 1, "text": "很少"},
                {"value": 2, "text": "有时"},
                {"value": 3, "text": "经常"},
                {"value": 4, "text": "持续"}
            ],
            "scoring": {
                "reverse_items": [2, 5, 6, 11, 12, 14, 16, 17, 18, 20],
                "ranges": [
                    {"min": 20, "max": 49, "result": "正常", "description": "您的抑郁程度在正常范围内。"},
                    {"min": 50, "max": 59, "result": "轻度抑郁", "description": "您有轻度抑郁症状，建议关注自己的情绪变化。"},
                    {"min": 60, "max": 69, "result": "中度抑郁", "description": "您有中度抑郁症状，建议寻求专业心理咨询。"},
                    {"min": 70, "max": 80, "result": "重度抑郁", "description": "您有重度抑郁症状，强烈建议尽快寻求专业心理医生的帮助。"}
                ]
            }
        },
        {
            "id": "sas",
            "title": "焦虑自评量表(SAS)",
            "description": "焦虑自评量表(Self-Rating Anxiety Scale, SAS)由Zung于1971年编制，用于评定焦虑症状的严重程度。",
            "instructions": "请仔细阅读每一条目，根据您最近一周的实际感受选择最符合的选项。",
            "questions": [
                {"id": 1, "text": "我感到比平时更紧张、焦虑"},
                {"id": 2, "text": "我无缘无故地感到害怕"},
                {"id": 3, "text": "我容易心烦意乱或感到恐慌"},
                {"id": 4, "text": "我感到我好像将要发疯"},
                {"id": 5, "text": "我感到一切都很好，不会发生什么不幸"},
                {"id": 6, "text": "我手脚发抖打颤"},
                {"id": 7, "text": "我因为头痛、颈痛和背痛而苦恼"},
                {"id": 8, "text": "我感到容易衰弱和疲乏"},
                {"id": 9, "text": "我感到心平气和，并且容易安静坐着"},
                {"id": 10, "text": "我感到心跳得很快"},
                {"id": 11, "text": "我因为一阵阵头晕而苦恼"},
                {"id": 12, "text": "我有晕倒发作或感到要晕倒似的"},
                {"id": 13, "text": "我吸气呼气都感到很容易"},
                {"id": 14, "text": "我的手脚麻木和刺痛"},
                {"id": 15, "text": "我因为胃痛和消化不良而苦恼"},
                {"id": 16, "text": "我常常要小便"},
                {"id": 17, "text": "我的手常常是干燥温暖的"},
                {"id": 18, "text": "我脸红发热"},
                {"id": 19, "text": "我容易入睡并且一夜睡得很好"},
                {"id": 20, "text": "我做恶梦"}
            ],
            "options": [
                {"value": 1, "text": "很少"},
                {"value": 2, "text": "有时"},
                {"value": 3, "text": "经常"},
                {"value": 4, "text": "持续"}
            ],
            "scoring": {
                "reverse_items": [5, 9, 13, 17, 19],
                "ranges": [
                    {"min": 20, "max": 49, "result": "正常", "description": "您的焦虑程度在正常范围内。"},
                    {"min": 50, "max": 59, "result": "轻度焦虑", "description": "您有轻度焦虑症状，建议关注自己的情绪变化。"},
                    {"min": 60, "max": 69, "result": "中度焦虑", "description": "您有中度焦虑症状，建议寻求专业心理咨询。"},
                    {"min": 70, "max": 80, "result": "重度焦虑", "description": "您有重度焦虑症状，强烈建议尽快寻求专业心理医生的帮助。"}
                ]
            }
        }
    ]
    with open(ASSESSMENTS_FILE, 'w', encoding='utf-8') as f:
        json.dump(default_assessments, f, ensure_ascii=False, indent=2)

# 初始化测评结果数据文件
if not os.path.exists(RESULTS_FILE):
    with open(RESULTS_FILE, 'w', encoding='utf-8') as f:
        json.dump([], f, ensure_ascii=False, indent=2)

def save_conversation(conversation_id, messages):
    """保存对话历史到文件"""
    filename = os.path.join(CONVERSATIONS_DIR, f"{conversation_id}.json")
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump({
            "id": conversation_id,
            "timestamp": datetime.now().isoformat(),
            "messages": messages
        }, f, ensure_ascii=False, indent=2)

def load_conversation(conversation_id):
    """从文件加载对话历史"""
    filename = os.path.join(CONVERSATIONS_DIR, f"{conversation_id}.json")
    if os.path.exists(filename):
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
            return data.get("messages", [])
    return []

# 问答模块辅助函数
def get_all_questions():
    """获取所有问题"""
    if os.path.exists(QUESTIONS_FILE):
        with open(QUESTIONS_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    return []

def save_questions(questions):
    """保存所有问题"""
    with open(QUESTIONS_FILE, 'w', encoding='utf-8') as f:
        json.dump(questions, f, ensure_ascii=False, indent=2)

def get_question(question_id):
    """获取特定问题"""
    questions = get_all_questions()
    for question in questions:
        if question["id"] == question_id:
            return question
    return None

def add_question(title, content, author="匿名用户"):
    """添加新问题"""
    questions = get_all_questions()
    question_id = str(uuid.uuid4())

    # 创建新问题
    new_question = {
        "id": question_id,
        "title": title,
        "content": content,
        "author": author,
        "timestamp": datetime.now().isoformat(),
        "answers": [],
        "votes": 0,
        "views": 0
    }

    questions.append(new_question)
    save_questions(questions)
    return question_id

def add_answer(question_id, content, author="匿名用户"):
    """添加回答"""
    questions = get_all_questions()

    for question in questions:
        if question["id"] == question_id:
            answer_id = str(uuid.uuid4())

            # 创建新回答
            new_answer = {
                "id": answer_id,
                "content": content,
                "author": author,
                "timestamp": datetime.now().isoformat(),
                "votes": 0
            }

            question["answers"].append(new_answer)
            save_questions(questions)
            return answer_id

    return None

def vote_question(question_id, vote_type):
    """对问题投票"""
    questions = get_all_questions()

    for question in questions:
        if question["id"] == question_id:
            if vote_type == "up":
                question["votes"] += 1
            elif vote_type == "down" and question["votes"] > 0:
                question["votes"] -= 1

            save_questions(questions)
            return question["votes"]

    return None

def vote_answer(question_id, answer_id, vote_type):
    """对回答投票"""
    questions = get_all_questions()

    for question in questions:
        if question["id"] == question_id:
            for answer in question["answers"]:
                if answer["id"] == answer_id:
                    if vote_type == "up":
                        answer["votes"] += 1
                    elif vote_type == "down" and answer["votes"] > 0:
                        answer["votes"] -= 1

                    save_questions(questions)
                    return answer["votes"]

    return None

def increment_view_count(question_id):
    """增加问题浏览次数"""
    questions = get_all_questions()

    for question in questions:
        if question["id"] == question_id:
            question["views"] += 1
            save_questions(questions)
            return question["views"]

    return None

# 心理测评模块辅助函数
def get_all_assessments():
    """获取所有心理测评"""
    if os.path.exists(ASSESSMENTS_FILE):
        with open(ASSESSMENTS_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    return []

def get_assessment(assessment_id):
    """获取特定心理测评"""
    assessments = get_all_assessments()
    for assessment in assessments:
        if assessment["id"] == assessment_id:
            return assessment
    return None

def get_all_results():
    """获取所有测评结果"""
    if os.path.exists(RESULTS_FILE):
        with open(RESULTS_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    return []

def save_results(results):
    """保存所有测评结果"""
    with open(RESULTS_FILE, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

def get_user_results(user_id):
    """获取特定用户的测评结果"""
    results = get_all_results()
    user_results = []
    for result in results:
        if result["user_id"] == user_id:
            user_results.append(result)
    return user_results

def save_assessment_result(user_id, assessment_id, answers, score, result_text, result_description):
    """保存测评结果"""
    results = get_all_results()

    # 创建新结果
    new_result = {
        "id": str(uuid.uuid4()),
        "user_id": user_id,
        "assessment_id": assessment_id,
        "timestamp": datetime.now().isoformat(),
        "answers": answers,
        "score": score,
        "result": result_text,
        "description": result_description
    }

    results.append(new_result)
    save_results(results)
    return new_result["id"]

def calculate_assessment_score(assessment_id, answers):
    """计算测评分数和结果"""
    assessment = get_assessment(assessment_id)
    if not assessment:
        return None, None, None

    # 计算原始分数
    raw_score = 0
    for question_id, answer_value in answers.items():
        question_id = int(question_id)
        # 处理反向计分题目
        if question_id in assessment["scoring"]["reverse_items"]:
            # 反向计分: 1->4, 2->3, 3->2, 4->1
            raw_score += 5 - answer_value
        else:
            raw_score += answer_value

    # 计算标准分 (SDS和SAS的标准分是原始分乘以1.25)
    standard_score = int(raw_score * 1.25)

    # 确定结果范围
    result_text = ""
    result_description = ""
    for range_info in assessment["scoring"]["ranges"]:
        if range_info["min"] <= standard_score <= range_info["max"]:
            result_text = range_info["result"]
            result_description = range_info["description"]
            break

    return standard_score, result_text, result_description

# 创建默认管理员账户
def create_default_admin():
    users = load_users()
    if not any(user.get('is_admin', False) for user in users):
        create_user('admin', '<EMAIL>', 'admin123', is_admin=True)
        print("已创建默认管理员账户: <EMAIL> / admin123")

# 创建默认管理员账户
create_default_admin()

# 认证相关路由
@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    if request.method == 'POST':
        email = request.form.get('email', '').strip()
        password = request.form.get('password', '')
        remember = 'remember' in request.form

        user = User.find_by_email(email)

        if user and check_password_hash(user.password_hash, password):
            login_user(user, remember=remember)
            next_page = request.args.get('next')
            if next_page and next_page.startswith('/'):
                return redirect(next_page)
            return redirect(url_for('index'))

        flash('邮箱或密码错误', 'danger')

    return render_template('login.html')

@app.route('/register', methods=['GET', 'POST'])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        email = request.form.get('email', '').strip()
        password = request.form.get('password', '')
        confirm_password = request.form.get('confirm_password', '')

        # 表单验证
        if not username or not email or not password:
            flash('所有字段都是必填的', 'danger')
        elif password != confirm_password:
            flash('两次输入的密码不一致', 'danger')
        elif len(password) < 6:
            flash('密码长度至少为6个字符', 'danger')
        elif not re.match(r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$', email):
            flash('请输入有效的邮箱地址', 'danger')
        else:
            # 创建用户
            success, message = create_user(username, email, password)
            if success:
                flash('注册成功，请登录', 'success')
                return redirect(url_for('login'))
            else:
                flash(message, 'danger')

    return render_template('register.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('您已成功退出登录', 'success')
    return redirect(url_for('login'))

@app.route('/profile')
@login_required
def profile():
    return render_template('profile.html')

@app.route('/')
def index():
    # 生成新的会话ID
    if 'conversation_id' not in session:
        session['conversation_id'] = str(uuid.uuid4())
    return render_template('index.html', models=AVAILABLE_MODELS)

# 问答模块路由
@app.route('/qa')
def qa_index():
    """问答模块首页"""
    questions = get_all_questions()
    # 按时间倒序排序
    questions.sort(key=lambda x: x["timestamp"], reverse=True)
    return render_template('qa_index.html', questions=questions)

@app.route('/qa/question/<question_id>')
def qa_question(question_id):
    """问题详情页"""
    question = get_question(question_id)
    if not question:
        return redirect(url_for('qa_index'))

    # 增加浏览次数
    increment_view_count(question_id)

    # 按投票数排序回答
    question["answers"].sort(key=lambda x: x["votes"], reverse=True)

    return render_template('qa_question.html', question=question)

@app.route('/qa/ask', methods=['GET', 'POST'])
@login_required
def qa_ask():
    """提问页面"""
    if request.method == 'POST':
        title = request.form.get('title', '').strip()
        content = request.form.get('content', '').strip()
        author = current_user.username  # 使用当前登录用户的用户名

        if not title or not content:
            return render_template('qa_ask.html', error="标题和内容不能为空")

        question_id = add_question(title, content, author)
        return redirect(url_for('qa_question', question_id=question_id))

    return render_template('qa_ask.html')

# 心理测评模块路由
@app.route('/assessments')
@login_required
def assessment_list():
    """心理测评列表页面"""
    assessments = get_all_assessments()
    user_results = get_user_results(current_user.id)

    # 为每个测评添加用户最近的结果
    for assessment in assessments:
        assessment["user_result"] = None
        for result in user_results:
            if result["assessment_id"] == assessment["id"]:
                if not assessment["user_result"] or result["timestamp"] > assessment["user_result"]["timestamp"]:
                    assessment["user_result"] = result

    return render_template('assessment_list.html', assessments=assessments)

@app.route('/assessments/<assessment_id>')
@login_required
def assessment_detail(assessment_id):
    """心理测评详情页面"""
    assessment = get_assessment(assessment_id)
    if not assessment:
        flash('测评不存在', 'danger')
        return redirect(url_for('assessment_list'))

    # 获取用户此测评的历史结果
    user_results = get_user_results(current_user.id)
    assessment_results = [r for r in user_results if r["assessment_id"] == assessment_id]
    assessment_results.sort(key=lambda x: x["timestamp"], reverse=True)

    return render_template('assessment_detail.html', assessment=assessment, results=assessment_results)

@app.route('/assessments/<assessment_id>/take', methods=['GET', 'POST'])
@login_required
def assessment_take(assessment_id):
    """参与心理测评页面"""
    assessment = get_assessment(assessment_id)
    if not assessment:
        flash('测评不存在', 'danger')
        return redirect(url_for('assessment_list'))

    if request.method == 'POST':
        # 收集答案
        answers = {}
        for question in assessment["questions"]:
            question_id = str(question["id"])
            if question_id in request.form:
                try:
                    answers[question_id] = int(request.form[question_id])
                except ValueError:
                    flash('请为所有问题选择答案', 'danger')
                    return render_template('assessment_take.html', assessment=assessment)

        # 确保所有问题都已回答
        if len(answers) != len(assessment["questions"]):
            flash('请为所有问题选择答案', 'danger')
            return render_template('assessment_take.html', assessment=assessment)

        # 计算分数和结果
        score, result_text, result_description = calculate_assessment_score(assessment_id, answers)

        # 保存结果
        result_id = save_assessment_result(
            current_user.id,
            assessment_id,
            answers,
            score,
            result_text,
            result_description
        )

        # 重定向到结果页面
        return redirect(url_for('assessment_result', result_id=result_id))

    return render_template('assessment_take.html', assessment=assessment)

@app.route('/assessments/results/<result_id>')
@login_required
def assessment_result(result_id):
    """测评结果页面"""
    # 获取结果
    results = get_all_results()
    result = None
    for r in results:
        if r["id"] == result_id:
            result = r
            break

    if not result:
        flash('结果不存在', 'danger')
        return redirect(url_for('assessment_list'))

    # 检查是否是当前用户的结果
    if result["user_id"] != current_user.id and not current_user.is_admin:
        flash('您无权查看此结果', 'danger')
        return redirect(url_for('assessment_list'))

    # 获取测评信息
    assessment = get_assessment(result["assessment_id"])
    if not assessment:
        flash('测评不存在', 'danger')
        return redirect(url_for('assessment_list'))

    return render_template('assessment_result.html', result=result, assessment=assessment)

@app.route('/api/chat', methods=['POST'])
def chat():
    data = request.json
    user_message = data.get('message', '')
    chat_history = data.get('history', [])
    model = data.get('model', 'deepseek-chat')
    system_message = data.get('system_message', DEFAULT_SYSTEM_MESSAGE)
    conversation_id = data.get('conversation_id', session.get('conversation_id', str(uuid.uuid4())))

    # 确保模型有效
    if model not in AVAILABLE_MODELS:
        model = 'deepseek-chat'

    # 构建消息列表，添加系统消息
    messages = [{"role": "system", "content": system_message}]
    messages.extend(chat_history)
    messages.append({"role": "user", "content": user_message})

    try:
        # 检查模型是否可用
        if model not in AVAILABLE_MODELS:
            return jsonify({
                "error": f"所选模型 '{model}' 不可用，请选择其他模型。",
                "available_models": list(AVAILABLE_MODELS.keys())
            }), 400

        # 调用DeepSeek模型
        try:
            response = client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=0.7,
                max_tokens=2000
            )

            # 获取回复内容
            reply = response.choices[0].message.content

            # 保存对话历史
            save_conversation(conversation_id, chat_history + [
                {"role": "user", "content": user_message},
                {"role": "assistant", "content": reply}
            ])

            return jsonify({
                "reply": reply,
                "conversation_id": conversation_id,
                "model": model
            })

        except Exception as api_error:
            error_message = str(api_error)
            if "Model Not Exist" in error_message:
                # 模型不存在错误，尝试使用默认模型
                print(f"模型 '{model}' 不存在，尝试使用默认模型 'deepseek-chat'")

                try:
                    response = client.chat.completions.create(
                        model="deepseek-chat",
                        messages=messages,
                        temperature=0.7,
                        max_tokens=2000
                    )

                    reply = response.choices[0].message.content

                    # 保存对话历史
                    save_conversation(conversation_id, chat_history + [
                        {"role": "user", "content": user_message},
                        {"role": "assistant", "content": reply}
                    ])

                    return jsonify({
                        "reply": reply,
                        "conversation_id": conversation_id,
                        "model": "deepseek-chat",
                        "warning": f"所选模型 '{model}' 不可用，已自动切换到 'deepseek-chat'"
                    })

                except Exception as fallback_error:
                    raise Exception(f"默认模型调用失败: {str(fallback_error)}")
            else:
                # 其他API错误
                raise

    except Exception as e:
        print(f"API调用错误: {str(e)}")
        return jsonify({
            "error": "抱歉，服务暂时不可用，请稍后再试。",
            "details": str(e)
        }), 500

@app.route('/api/conversations', methods=['GET'])
def list_conversations():
    """获取所有对话历史列表"""
    conversations = []
    for filename in os.listdir(CONVERSATIONS_DIR):
        if filename.endswith('.json'):
            with open(os.path.join(CONVERSATIONS_DIR, filename), 'r', encoding='utf-8') as f:
                data = json.load(f)
                conversations.append({
                    "id": data.get("id"),
                    "timestamp": data.get("timestamp"),
                    "preview": data.get("messages", [])[-1].get("content", "")[:50] if data.get("messages") else ""
                })
    return jsonify({"conversations": sorted(conversations, key=lambda x: x["timestamp"], reverse=True)})

@app.route('/api/conversations/<conversation_id>', methods=['GET'])
def get_conversation(conversation_id):
    """获取特定对话历史"""
    messages = load_conversation(conversation_id)
    return jsonify({"messages": messages})

@app.route('/api/conversations/<conversation_id>', methods=['DELETE'])
def delete_conversation(conversation_id):
    """删除特定对话历史"""
    filename = os.path.join(CONVERSATIONS_DIR, f"{conversation_id}.json")
    if os.path.exists(filename):
        os.remove(filename)
        return jsonify({"success": True})
    return jsonify({"success": False, "error": "对话不存在"}), 404

# 问答模块API
@app.route('/api/qa/questions', methods=['GET'])
def api_get_questions():
    """获取所有问题的API"""
    questions = get_all_questions()
    # 按时间倒序排序
    questions.sort(key=lambda x: x["timestamp"], reverse=True)
    return jsonify({"questions": questions})

@app.route('/api/qa/questions', methods=['POST'])
@login_required
def api_add_question():
    """添加问题的API"""
    data = request.json
    title = data.get('title', '').strip()
    content = data.get('content', '').strip()
    author = current_user.username  # 使用当前登录用户的用户名

    if not title or not content:
        return jsonify({"error": "标题和内容不能为空"}), 400

    question_id = add_question(title, content, author)
    return jsonify({"question_id": question_id, "success": True})

@app.route('/api/qa/questions/<question_id>', methods=['GET'])
def api_get_question(question_id):
    """获取特定问题的API"""
    question = get_question(question_id)
    if not question:
        return jsonify({"error": "问题不存在"}), 404

    # 增加浏览次数
    increment_view_count(question_id)

    # 按投票数排序回答
    question["answers"].sort(key=lambda x: x["votes"], reverse=True)

    return jsonify({"question": question})

@app.route('/api/qa/questions/<question_id>/answers', methods=['POST'])
@login_required
def api_add_answer(question_id):
    """添加回答的API"""
    data = request.json
    content = data.get('content', '').strip()
    author = current_user.username  # 使用当前登录用户的用户名

    if not content:
        return jsonify({"error": "回答内容不能为空"}), 400

    answer_id = add_answer(question_id, content, author)
    if not answer_id:
        return jsonify({"error": "问题不存在"}), 404

    return jsonify({"answer_id": answer_id, "success": True})

@app.route('/api/qa/questions/<question_id>/vote', methods=['POST'])
@login_required
def api_vote_question(question_id):
    """对问题投票的API"""
    data = request.json
    vote_type = data.get('vote_type')

    if vote_type not in ['up', 'down']:
        return jsonify({"error": "无效的投票类型"}), 400

    votes = vote_question(question_id, vote_type)
    if votes is None:
        return jsonify({"error": "问题不存在"}), 404

    return jsonify({"votes": votes, "success": True})

@app.route('/api/qa/questions/<question_id>/answers/<answer_id>/vote', methods=['POST'])
@login_required
def api_vote_answer(question_id, answer_id):
    """对回答投票的API"""
    data = request.json
    vote_type = data.get('vote_type')

    if vote_type not in ['up', 'down']:
        return jsonify({"error": "无效的投票类型"}), 400

    votes = vote_answer(question_id, answer_id, vote_type)
    if votes is None:
        return jsonify({"error": "问题或回答不存在"}), 404

    return jsonify({"votes": votes, "success": True})

# 管理员仪表盘路由
@app.route('/admin')
@login_required
@admin_required
def admin_dashboard():
    """管理员仪表盘首页"""
    # 统计数据
    questions = get_all_questions()
    users = load_users()
    assessments = get_all_assessments()
    assessment_results = get_all_results()

    # 问题统计
    total_questions = len(questions)
    total_answers = sum(len(q['answers']) for q in questions)
    total_views = sum(q['views'] for q in questions)
    total_votes = sum(q['votes'] for q in questions)

    # 用户统计
    total_users = len(users)
    admin_users = sum(1 for user in users if user.get('is_admin', False))

    # 测评统计
    total_assessments = len(assessments)
    total_assessment_results = len(assessment_results)

    # 最近注册的用户
    recent_users = sorted(users, key=lambda x: x.get('created_at', ''), reverse=True)[:5]

    # 热门问题
    popular_questions = sorted(questions, key=lambda x: x['views'], reverse=True)[:5]

    # 最近的测评结果
    recent_results = sorted(assessment_results, key=lambda x: x["timestamp"], reverse=True)[:5]

    # 为最近的测评结果添加用户和测评信息
    for result in recent_results:
        # 添加用户信息
        result["user"] = None
        for user in users:
            if user["id"] == result["user_id"]:
                result["user"] = user
                break

        # 添加测评信息
        result["assessment"] = None
        for assessment in assessments:
            if assessment["id"] == result["assessment_id"]:
                result["assessment"] = assessment
                break

    return render_template('admin/dashboard.html',
                          total_questions=total_questions,
                          total_answers=total_answers,
                          total_views=total_views,
                          total_votes=total_votes,
                          total_users=total_users,
                          admin_users=admin_users,
                          total_assessments=total_assessments,
                          total_assessment_results=total_assessment_results,
                          recent_users=recent_users,
                          popular_questions=popular_questions,
                          recent_results=recent_results)

@app.route('/admin/questions')
@login_required
@admin_required
def admin_questions():
    """管理员问题管理页面"""
    questions = get_all_questions()
    # 按时间倒序排序
    questions.sort(key=lambda x: x["timestamp"], reverse=True)
    return render_template('admin/questions.html', questions=questions)

@app.route('/admin/users')
@login_required
@admin_required
def admin_users():
    """管理员用户管理页面"""
    users = load_users()
    # 按注册时间倒序排序
    users.sort(key=lambda x: x.get('created_at', ''), reverse=True)
    return render_template('admin/users.html', users=users)

@app.route('/admin/assessments')
@login_required
@admin_required
def admin_assessments():
    """管理员心理测评管理页面"""
    assessments = get_all_assessments()
    results = get_all_results()

    # 为每个测评添加统计信息
    for assessment in assessments:
        assessment_results = [r for r in results if r["assessment_id"] == assessment["id"]]
        assessment["total_results"] = len(assessment_results)

        # 计算各结果类型的数量
        result_counts = {}
        for result in assessment_results:
            result_type = result["result"]
            result_counts[result_type] = result_counts.get(result_type, 0) + 1
        assessment["result_counts"] = result_counts

    return render_template('admin/assessments.html', assessments=assessments)

@app.route('/admin/assessment_results')
@login_required
@admin_required
def admin_assessment_results():
    """管理员测评结果管理页面"""
    results = get_all_results()
    assessments = get_all_assessments()
    users = load_users()

    # 按时间倒序排序
    results.sort(key=lambda x: x["timestamp"], reverse=True)

    # 添加用户和测评信息
    for result in results:
        # 添加用户信息
        result["user"] = None
        for user in users:
            if user["id"] == result["user_id"]:
                result["user"] = user
                break

        # 添加测评信息
        result["assessment"] = None
        for assessment in assessments:
            if assessment["id"] == result["assessment_id"]:
                result["assessment"] = assessment
                break

    return render_template('admin/assessment_results.html', results=results)

@app.route('/admin/assessment_results/<result_id>')
@login_required
@admin_required
def admin_assessment_result_detail(result_id):
    """管理员测评结果详情页面"""
    # 获取结果
    results = get_all_results()
    result = None
    for r in results:
        if r["id"] == result_id:
            result = r
            break

    if not result:
        flash('结果不存在', 'danger')
        return redirect(url_for('admin_assessment_results'))

    # 获取测评信息
    assessment = get_assessment(result["assessment_id"])
    if not assessment:
        flash('测评不存在', 'danger')
        return redirect(url_for('admin_assessment_results'))

    # 获取用户信息
    users = load_users()
    user = None
    for u in users:
        if u["id"] == result["user_id"]:
            user = u
            break

    return render_template('admin/assessment_result_detail.html', result=result, assessment=assessment, user=user)

@app.route('/admin/analytics')
@login_required
@admin_required
def admin_analytics():
    """管理员数据分析页面"""
    questions = get_all_questions()

    # 统计数据
    total_questions = len(questions)
    total_answers = sum(len(q['answers']) for q in questions)
    total_views = sum(q['views'] for q in questions)
    total_votes = sum(q['votes'] for q in questions)

    # 问题和回答数量随时间变化
    question_dates = [q['timestamp'].split('T')[0] for q in questions]
    question_date_counts = {}
    for date in question_dates:
        question_date_counts[date] = question_date_counts.get(date, 0) + 1

    # 回答日期
    answer_dates = []
    for q in questions:
        for a in q['answers']:
            answer_dates.append(a['timestamp'].split('T')[0])

    answer_date_counts = {}
    for date in answer_dates:
        answer_date_counts[date] = answer_date_counts.get(date, 0) + 1

    # 合并日期
    all_dates = sorted(set(question_date_counts.keys()) | set(answer_date_counts.keys()))

    # 准备图表数据
    date_data = []
    for date in all_dates:
        date_data.append({
            'date': date,
            'questions': question_date_counts.get(date, 0),
            'answers': answer_date_counts.get(date, 0)
        })

    # 浏览量和投票数统计
    view_ranges = [0, 5, 10, 20, 50, 100, 500]
    view_stats = {}
    for i in range(len(view_ranges)):
        if i == 0:
            view_stats[f"{view_ranges[i]}"] = 0
        elif i == len(view_ranges) - 1:
            view_stats[f"{view_ranges[i-1]}+"] = 0
        else:
            view_stats[f"{view_ranges[i-1]}-{view_ranges[i]-1}"] = 0

    for q in questions:
        views = q['views']
        for i in range(len(view_ranges)):
            if i == 0 and views == 0:
                view_stats[f"{view_ranges[i]}"] += 1
                break
            elif i == len(view_ranges) - 1 and views >= view_ranges[i-1]:
                view_stats[f"{view_ranges[i-1]}+"] += 1
                break
            elif i > 0 and view_ranges[i-1] <= views < view_ranges[i]:
                view_stats[f"{view_ranges[i-1]}-{view_ranges[i]-1}"] += 1
                break

    # 投票数统计
    vote_ranges = [-10, -5, 0, 5, 10, 20, 50]
    vote_stats = {}
    for i in range(len(vote_ranges)):
        if i == 0:
            vote_stats[f"<{vote_ranges[i]}"] = 0
        elif i == len(vote_ranges) - 1:
            vote_stats[f"{vote_ranges[i-1]}+"] = 0
        else:
            vote_stats[f"{vote_ranges[i-1]}-{vote_ranges[i]-1}"] = 0

    for q in questions:
        votes = q['votes']
        for i in range(len(vote_ranges)):
            if i == 0 and votes < vote_ranges[i]:
                vote_stats[f"<{vote_ranges[i]}"] += 1
                break
            elif i == len(vote_ranges) - 1 and votes >= vote_ranges[i-1]:
                vote_stats[f"{vote_ranges[i-1]}+"] += 1
                break
            elif i > 0 and vote_ranges[i-1] <= votes < vote_ranges[i]:
                vote_stats[f"{vote_ranges[i-1]}-{vote_ranges[i]-1}"] += 1
                break

    return render_template(
        'admin/analytics.html',
        total_questions=total_questions,
        total_answers=total_answers,
        total_views=total_views,
        total_votes=total_votes,
        date_data=date_data,
        view_stats=view_stats,
        vote_stats=vote_stats
    )

# 管理员操作API
@app.route('/api/admin/questions/<question_id>', methods=['DELETE'])
@login_required
@admin_required
def admin_delete_question(question_id):
    """管理员删除问题"""
    questions = get_all_questions()

    # 查找并删除问题
    for i, question in enumerate(questions):
        if question['id'] == question_id:
            del questions[i]
            save_questions(questions)
            return jsonify({"success": True})

    return jsonify({"success": False, "error": "问题不存在"}), 404

@app.route('/api/admin/questions/<question_id>/answers/<answer_id>', methods=['DELETE'])
@login_required
@admin_required
def admin_delete_answer(question_id, answer_id):
    """管理员删除回答"""
    questions = get_all_questions()

    # 查找问题
    for question in questions:
        if question['id'] == question_id:
            # 查找并删除回答
            for i, answer in enumerate(question['answers']):
                if answer['id'] == answer_id:
                    del question['answers'][i]
                    save_questions(questions)
                    return jsonify({"success": True})

    return jsonify({"success": False, "error": "问题或回答不存在"}), 404

@app.route('/api/admin/users/<user_id>', methods=['DELETE'])
@login_required
@admin_required
def admin_delete_user(user_id):
    """管理员删除用户"""
    users = load_users()

    # 不能删除自己
    if user_id == current_user.id:
        return jsonify({"success": False, "error": "不能删除自己的账户"}), 400

    # 查找并删除用户
    for i, user in enumerate(users):
        if user['id'] == user_id:
            del users[i]
            save_users(users)
            return jsonify({"success": True})

    return jsonify({"success": False, "error": "用户不存在"}), 404

@app.route('/api/admin/users/<user_id>/admin', methods=['POST'])
@login_required
@admin_required
def admin_toggle_admin(user_id):
    """管理员切换用户的管理员权限"""
    users = load_users()

    # 不能修改自己的权限
    if user_id == current_user.id:
        return jsonify({"success": False, "error": "不能修改自己的管理员权限"}), 400

    # 查找并修改用户
    for user in users:
        if user['id'] == user_id:
            user['is_admin'] = not user.get('is_admin', False)
            save_users(users)
            return jsonify({"success": True, "is_admin": user['is_admin']})

    return jsonify({"success": False, "error": "用户不存在"}), 404

# 心理测评管理API
@app.route('/api/admin/assessment_results/<result_id>', methods=['DELETE'])
@login_required
@admin_required
def admin_delete_assessment_result(result_id):
    """管理员删除测评结果"""
    results = get_all_results()

    # 查找并删除结果
    for i, result in enumerate(results):
        if result['id'] == result_id:
            del results[i]
            save_results(results)
            return jsonify({"success": True})

    return jsonify({"success": False, "error": "结果不存在"}), 404

@app.route('/api/admin/assessment_results/user/<user_id>', methods=['GET'])
@login_required
@admin_required
def admin_get_user_assessment_results(user_id):
    """获取特定用户的所有测评结果"""
    results = get_all_results()
    user_results = [r for r in results if r["user_id"] == user_id]

    # 按时间倒序排序
    user_results.sort(key=lambda x: x["timestamp"], reverse=True)

    # 添加测评信息
    assessments = get_all_assessments()
    for result in user_results:
        result["assessment"] = None
        for assessment in assessments:
            if assessment["id"] == result["assessment_id"]:
                result["assessment"] = {
                    "id": assessment["id"],
                    "title": assessment["title"]
                }
                break

    return jsonify({"results": user_results})

if __name__ == '__main__':
    app.run(debug=True, port=5002)